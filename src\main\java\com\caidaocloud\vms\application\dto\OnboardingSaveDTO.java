package com.caidaocloud.vms.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 预入职保存信息DTO
 *
 * <AUTHOR>
 * @date 2025/10/30
 */
@Data
@ApiModel(description = "预入职保存信息")
public class OnboardingSaveDTO {

    // VMS相关信息
    @ApiModelProperty(value = "供应商ID", required = true)
    @NotBlank(message = "供应商ID不能为空")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierTxt;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    @ApiModelProperty(value = "岗位ID", required = true)
    @NotBlank(message = "岗位ID不能为空")
    private String positionId;

    @ApiModelProperty(value = "管理费")
    private BigDecimal managementFee;

    @ApiModelProperty(value = "备注")
    private String remark;

    // 子DTO
    @ApiModelProperty(value = "工作信息", required = true)
    @NotNull(message = "工作信息不能为空")
    @Valid
    private OnboardingWorkInfoDTO workInfo;

    @ApiModelProperty(value = "个人信息")
    @Valid
    private OnboardingPrivateInfoDTO privateInfo;

    @ApiModelProperty(value = "合同信息")
    @Valid
    private OnboardingContractDTO contractInfo;

    @ApiModelProperty(value = "薪资信息", required = true)
    @NotNull(message = "薪资信息不能为空")
    @Valid
    private OnboardingSalaryDTO salaryInfo;

    @ApiModelProperty(value = "其他合同信息")
    @Valid
    private OnboardingOtherContractDTO otherContractInfo;

    @ApiModelProperty(value = "教育经历列表")
    @Valid
    private List<OnboardingEducationDTO> educationExperiences;

    /**
     * 工作信息DTO
     */
    @Data
    @ApiModel(description = "员工工作信息")
    public static class OnboardingWorkInfoDTO {
        private String workno;

        private String name;

        private String enName;

        private Long hireDate;


        private EnumSimple empStatus;

        private String photo;

        private String leadEmpId;

        private String organize;

        private String organizeTxt;

        private String jobGrade;

        private String job;

        private String jobTxt;

        private String post;

        private String postTxt;

        private EnumSimple probation;

        private Long confirmationDate;

        private EnumSimple confirmationStatus;

        private DictSimple empType;

        private Long leaveDate;

        private EnumSimple workHour;

        private Double divisionAge;

        private Double divisionAgeAdjust;

        private String companyEmail;

        private String workplace;

        private String workplaceTxt;

        private DictSimple joinCompanyWay;

        private String costCenters;

        private String company;

        private String companyTxt;

        private Long expectGraduateDate;

        private String businessLineId;

        private DictSimple contractType;

        private Long trainingDate;

        private Boolean reentry = false;

        private String reentryType;

        private String reentryId;

        private String leaderOrganize;

        private String leaderOrganizeTxt;

        private String leaderPost;

        private String leaderPostTxt;

        private String recruitment;

        private Boolean completed = false;
    }

    /**
     * 个人信息DTO
     */
    @Data
    @ApiModel(description = "员工个人信息")
    public static class OnboardingPrivateInfoDTO {
        private String name;

        private String enName;

        private DictSimple sex;

        private DictSimple nationality;

        private DictSimple nation;

        private String nativePlace;

        private DictSimple familyType;

        private String permanentAddress;

        private Long birthDate;

        private Integer divisionAge;

        private EnumSimple maritalStatus;

        private EnumSimple fertilityStatus;

        private DictSimple politicalOutlook;

        private String phone;

        private String email;

        private String postalAddress;

        private EnumSimple cardType;

        private String cardNo;

        private Long cardEffectiveDate;

        private Boolean disability = false;

        private String guardianName;

        private String guardianPhone;

        private String guardianEmail;

        private Long workingStartDate;

        private Boolean adult = false;

        private Boolean criminal = false;

        private String crimeRecord;

        private String permanentProvince;

        private String familyProvince;

    }

    /**
     * 合同信息DTO
     */
    @Data
    @ApiModel(description = "合同信息")
    public static class OnboardingContractDTO {

        private DictSimple contractType;

        private String contractNo;

        private Integer contractPeriod;

        private Long startDate;

        private Long endDate;

        private EnumSimple probationPeriod; // TODO: implement enum

        private Long probationPeriodBeginDate;

        private Long probationPeriodEndDate;

        private EnumSimple probation; // TODO: implement enum

        private EnumSimple periodType; // TODO: implement enum

        private EnumSimple signType; // TODO: implement enum

        private String contractTypeSet;

        private String contractTypeSetTxt;

        private Double contractYear;
    }

    /**
     * 薪资信息DTO
     */
    @Data
    @ApiModel(description = "薪资信息")
    public static class OnboardingSalaryDTO {
        @ApiModelProperty(value = "薪资", required = true)
        private String salary;

        @ApiModelProperty(value = "报价方式", required = true)
        private EnumSimple quotationMode;
        private String quotationValue;
    }

    /**
     * 其他合同信息DTO
     */
    @Data
    @ApiModel(description = "其他合同信息")
    public static class OnboardingOtherContractDTO {
        private Boolean signAgreement = false;

        private Long startDate;

        private Long endDate;
    }

    /**
     * 教育经历DTO
     */
    @Data
    @ApiModel(description = "教育经历")
    public static class OnboardingEducationDTO {
        private String school;

        private String address;

        private Long startDate;

        private Long endDate;

        private Boolean degreeReceived = false;

        private EnumSimple lastYear; // TODO: enum

        private DictSimple degree;

        private DictSimple background;

        private String major;

        private String minor;

        private EnumSimple educationForm; // TODO: enum

        private String certificateNumber;

        private EnumSimple partTimeType; // TODO: enum

        private String remark;
    }
}