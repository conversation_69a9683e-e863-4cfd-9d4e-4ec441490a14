package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.vms.application.dto.ProjectSettingDto;
import com.caidaocloud.vms.application.service.ProjectSettingService;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 项目设置控制器
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project/setting")
@Api(tags = "项目设置管理")
public class ProjectSettingController {

    @Autowired
    private ProjectSettingService projectSettingService;

    /**
     * 保存或更新项目设置
     * 
     * @param settingDto 项目设置信息
     * @return 操作结果
     */
    @PostMapping("/edit")
    @ApiOperation(value = "保存项目设置", notes = "创建或更新项目设置信息")
    public Result saveProjectSetting(
            @ApiParam(value = "项目设置信息", required = true) @RequestBody ProjectSettingDto settingDto) {

        // 手动校验必填字段
        if (settingDto.getProjectId() == null || settingDto.getProjectId().trim().isEmpty()) {
            return Result.fail("项目ID不能为空");
        }

        projectSettingService.edit(settingDto);
        return Result.ok();
    }

    /**
     * 根据项目ID获取项目设置
     * 
     * @param projectId 项目ID
     * @return 项目设置信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "根据项目ID获取项目设置", notes = "根据项目ID获取对应的项目设置信息")
    public Result<ProjectSettingVO> getByProjectId(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        ProjectSettingVO settingVO = projectSettingService.getByProjectId(projectId);
        return Result.ok(settingVO);
    }

    /**
     * 提交项目设置变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit")
    @ApiOperation(value = "提交项目设置变更", notes = "提交项目设置的所有变更，生成历史记录并发起工作流")
    public Result<Void> commitProjectSetting(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        projectSettingService.commitProjectSetting(projectId);
        return Result.success();
    }
}
