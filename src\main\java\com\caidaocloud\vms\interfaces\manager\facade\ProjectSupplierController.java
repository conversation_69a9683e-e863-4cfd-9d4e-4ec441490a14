package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectSupplierDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.application.service.ProjectSupplierService;
import com.caidaocloud.vms.application.vo.ProjectSupplierVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 项目供应商关系控制器
 * 
 * <AUTHOR>
 * @date 2025/6/10
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project/supplier")
@Api(tags = "项目供应商关系", description = "项目供应商关系的增删改查接口")
public class ProjectSupplierController {

    @Autowired
    private ProjectSupplierService projectSupplierService;

    /**
     * 添加项目供应商关系
     * 
     * @param projectSupplierDto 项目供应商关系信息
     * @return 操作结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "添加项目供应商关系", notes = "为项目添加供应商关系")
    public Result<String> addProjectSupplier(
            @ApiParam(value = "项目供应商关系信息", required = true) @RequestBody ProjectSupplierDto projectSupplierDto) {

        // 手动校验必填字段
        if (projectSupplierDto.getProjectId() == null || projectSupplierDto.getProjectId().trim().isEmpty()) {
            return Result.fail("项目ID不能为空");
        }
        if (projectSupplierDto.getSupplierId() == null || projectSupplierDto.getSupplierId().trim().isEmpty()) {
            return Result.fail("供应商ID不能为空");
        }

        String id = projectSupplierService.addProjectSupplier(projectSupplierDto);
        return Result.ok(id);

    }
    //
    // /**
    // * 更新项目供应商关系
    // *
    // * @param projectSupplierDto 项目供应商关系信息
    // * @return 操作结果
    // */
    // @PostMapping("/update")
    // @ApiOperation(value = "更新项目供应商关系", notes = "更新项目供应商关系信息")
    // public Result updateProjectSupplier(
    // @ApiParam(value = "项目供应商关系信息", required = true)
    // @RequestBody ProjectSupplierDto projectSupplierDto) {
    //
    // // 手动校验必填字段
    // if (projectSupplierDto.getProjectId() == null ||
    // projectSupplierDto.getProjectId().trim().isEmpty()) {
    // return Result.fail("关系ID不能为空");
    // }
    //
    // projectSupplierService.updateProjectSupplier(projectSupplierDto);
    // return Result.ok();
    //
    // }

    /**
     * 删除项目供应商关系
     * 
     * @param id 关系ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除项目供应商关系", notes = "删除项目供应商关系")
    public Result deleteProjectSupplier(
            @RequestBody ProjectSupplierDto dto) {

        projectSupplierService.deleteProjectSupplier(dto);
        return Result.ok();

    }

    /**
     * 获取项目供应商列表
     * 
     * @param queryDTO 查询条件
     * @return 供应商列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取项目供应商列表", notes = "分页查询项目供应商列表")
    public Result<PageResult<ProjectSupplierVO>> getProjectSuppliers(
            @ApiParam(value = "查询条件", required = true) @RequestBody ProjectSupplierQueryDTO queryDTO) {

        // 手动校验必填字段
        if (queryDTO.getProjectId() == null || queryDTO.getProjectId().trim().isEmpty()) {
            return Result.fail("项目ID不能为空");
        }

        PageResult<ProjectSupplierVO> result = projectSupplierService.getProjectSuppliers(queryDTO);
        return Result.ok(result);

    }

    /**
     * 提交项目供应商变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit")
    @ApiOperation(value = "提交项目供应商变更", notes = "提交项目供应商的所有变更，生成历史记录并发起工作流")
    public Result<Void> commitProjectSupplier(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        projectSupplierService.commitProjectSupplier(projectId);
        return Result.success();
    }

}