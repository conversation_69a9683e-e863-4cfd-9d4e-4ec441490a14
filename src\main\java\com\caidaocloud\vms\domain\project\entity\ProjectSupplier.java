package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.project.enums.InviteStatus;
import com.caidaocloud.vms.domain.project.history.SimplifiedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProjectSupplier extends SimplifiedHistoryFormat<ProjectSupplier> {

    private String projectId;

    private String supplierId;

    private String supplierCode;

    private String supplierName;

    private InviteStatus status;

    public static String identifier = "entity.vms.ProjectSupplier";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }


    public ProjectSupplier(String projectId, String supplierId) {
        this.projectId = projectId;
        this.supplierId = supplierId;
        status = InviteStatus.INITIATED;
    }

    @Override
    public String formatDisplay() {
        // TODO: 2025/11/2 load code
        return String.format("%s,%s", supplierCode,supplierName);
    }


}