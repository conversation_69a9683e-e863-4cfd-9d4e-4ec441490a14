package com.caidaocloud.vms.domain.project.entity;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.annotation.ArrayEntityFormat;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ProjectDraft extends BaseEntity {

    private String targetId;
    private String projectId;
    private String positionId;

    private EnumSimple type;
    private EnumSimple subType;
    private EnumSimple operation;

    // TODO: 2025/10/24 第一次创建时，记录origin ，后续根据origin和snapshot生成change
    private String origin;
    private String snapshot;

    public static String identifier = "entity.vms.ProjectDraft";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }
}