package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class ProjectSetting extends DetailedHistoryFormat<ProjectSetting> {

    private String projectId;

    private Boolean budgetEnabled = false;

    private Boolean quoteEnabled = false;

    private Boolean headcountEnabled = false;

    private Boolean positionAutoClose = false;

    private Boolean projectAutoClose = false;

    private Boolean positionApprovalFlow = false;

    private Boolean preHireEnabled = false;

    public static String identifier = "entity.vms.ProjectSetting";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

}