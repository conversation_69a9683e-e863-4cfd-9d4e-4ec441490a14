package com.caidaocloud.vms.infrastructure.repository;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class ProjectRepositoryImpl implements ProjectRepository {

    @Override
    @HistoryDetailRecord(entityTypes = { Project.class }, historyType = HistoryType.BASIC_INFO,operationType = OperationType.CREATE_OR_UPDATE)
    public String saveOrUpdate(Project project) {
        if (project.getBid() == null) {
            DataInsert.identifier(Project.identifier).insert(project);
        } else {
            DataUpdate.identifier(Project.identifier).update(project);
        }
        return project.getBid();
    }

    @Override
    public Project getById(String projectId) {
        Project project = DataQuery.identifier(Project.identifier).oneOrNull(projectId, Project.class);
        if (project != null) {
            // 加载项目设置
            ProjectSetting projectSetting = DataQuery.identifier(ProjectSetting.identifier)
                    .oneOrNull(projectId, ProjectSetting.class);
            project.setProjectSetting(projectSetting);
        }
        return project;
    }

    @Override
    public PageResult<Project> findByPage(String projectName, ProjectQueryDTO queryDTO) {
        return DataQuery.identifier(Project.identifier)
                .limit(queryDTO.getPageSize(), queryDTO.getPageNo())
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andRegexIf("projectName", projectName, () -> StringUtils.isNotEmpty(projectName)),
                        Project.class);
    }

    @Override
    public void delete(Project project) {
        DataDelete.identifier(Project.identifier).delete(project.getBid());
    }

    @Override
    public List<Project> loadList(List<String> projectIds) {
        return DataQuery.identifier(Project.identifier)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                                .andIn("bid", projectIds),
                        Project.class).getItems();
    }
}