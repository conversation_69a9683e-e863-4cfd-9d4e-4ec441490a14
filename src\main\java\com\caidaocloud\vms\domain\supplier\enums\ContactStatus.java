package com.caidaocloud.vms.domain.supplier.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

public enum ContactStatus {
    ACTIVE(0, "Active"),
    INACTIVE(1, "Inactive");

    private final int code;
    private final String description;

    ContactStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.code));
        return enumSimple;
    }

    public static ContactStatus fromCode(int code) {
        for (ContactStatus status : ContactStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new ServerException("Invalid ContactStatus code: " + code);
    }
}