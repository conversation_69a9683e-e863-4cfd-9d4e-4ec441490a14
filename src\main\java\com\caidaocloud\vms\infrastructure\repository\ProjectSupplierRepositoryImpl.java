package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectSupplierRepository;
import com.caidaocloud.vms.domain.project.annotation.HistoryDetailRecord;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class ProjectSupplierRepositoryImpl implements ProjectSupplierRepository {

    @Override
    @HistoryDetailRecord(entityTypes = {
            ProjectSupplier.class }, historyType = HistoryType.SUPPLIER, operationType = OperationType.CREATE_OR_UPDATE)
    public String saveOrUpdate(ProjectSupplier projectSupplier) {
        if (projectSupplier.getBid() == null) {
            DataInsert.identifier(ProjectSupplier.identifier).insert(projectSupplier);
        } else {
            DataUpdate.identifier(ProjectSupplier.identifier).update(projectSupplier);
        }
        return projectSupplier.getBid();
    }

    @Override
    public ProjectSupplier getById(String id) {
        return DataQuery.identifier(ProjectSupplier.identifier).oneOrNull(id, ProjectSupplier.class);
    }

    @Override
    public List<ProjectSupplier> findByProjectId(String projectId) {
        return DataQuery.identifier(ProjectSupplier.identifier)
                .filter(DataFilter.eq("projectId", projectId)
                        .and(DataFilter.ne("deleted", Boolean.TRUE.toString())), ProjectSupplier.class)
                .getItems();
    }

    @Override
    public PageResult<ProjectSupplier> findByPage(String projectId, String supplierName,
            ProjectSupplierQueryDTO queryDTO) {
        return DataQuery.identifier(ProjectSupplier.identifier)
                .limit(queryDTO.getPageSize(), queryDTO.getPageNo())
                .filter(DataFilter.eq("projectId", projectId)
                        .and(DataFilter.ne("deleted", Boolean.TRUE.toString())), ProjectSupplier.class);
    }

    @Override
    @HistoryDetailRecord(entityTypes = {
            ProjectSupplier.class }, historyType = HistoryType.SUPPLIER, operationType = OperationType.DELETE)
    public void delete(ProjectSupplier supplier) {
        DataDelete.identifier(ProjectSupplier.identifier).delete(supplier.getBid());
    }

    @Override
    public boolean existsByProjectIdAndSupplierId(String projectId, String supplierId) {
        return DataQuery.identifier(ProjectSupplier.identifier)
                .filter(DataFilter.eq("projectId", projectId)
                        .and(DataFilter.eq("supplierId", supplierId))
                        .and(DataFilter.ne("deleted", Boolean.TRUE.toString())), ProjectSupplier.class)
                .getTotal() > 0;
    }

    @Override
    public Optional<ProjectSupplier> getSupplier(String supplierId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectSupplier.identifier)
                .oneOrNull(supplierId, ProjectSupplier.class));
    }

    @Override
    public void deleteSupplier(ProjectSupplier supplier) {
        DataDelete.identifier(ProjectSupplier.identifier).delete(supplier.getBid());
    }

    @Override
    public List<ProjectSupplier> findBySupplierId(String supplierId) {
        return DataQuery.identifier(ProjectSupplier.identifier)
                .filter(DataFilter.eq("supplierId", supplierId)
                        .and(DataFilter.ne("deleted", Boolean.TRUE.toString())), ProjectSupplier.class)
                .getItems();
    }
}