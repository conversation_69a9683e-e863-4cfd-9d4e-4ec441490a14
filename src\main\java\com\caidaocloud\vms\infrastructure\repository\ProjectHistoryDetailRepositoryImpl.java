package com.caidaocloud.vms.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryDetailRepository;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史详情Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/9/27
 */
@Repository
public class ProjectHistoryDetailRepositoryImpl implements ProjectHistoryDetailRepository {

    @Override
    public String saveOrUpdate(ProjectHistoryDetail detail) {
        if (detail.getBid() == null) {
            DataInsert.identifier(ProjectHistoryDetail.identifier).insert(detail);
        } else {
            DataUpdate.identifier(ProjectHistoryDetail.identifier).update(detail);
        }
        return detail.getBid();
    }

    @Override
    public Optional<ProjectHistoryDetail> getById(String detailId) {
        return Optional.ofNullable(DataQuery.identifier(ProjectHistoryDetail.identifier)
                .oneOrNull(detailId, ProjectHistoryDetail.class));
    }

    @Override
    public List<ProjectHistoryDetail> getByHistoryId(String historyId) {
        return DataQuery.identifier(ProjectHistoryDetail.identifier)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("historyId", historyId), ProjectHistoryDetail.class).getItems();
    }

    @Override
    public void delete(String detailId) {
        DataDelete.identifier(ProjectHistoryDetail.identifier).delete(detailId);
    }

    @Override
    public PageResult<ProjectHistoryDetail> findApprovedByPage(ProjectHistoryQueryDTO queryDTO) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("approveStatus", String.valueOf(ProjectStatus.APPROVED.getCode()))
                .andEq("projectId", queryDTO.getProjectId());

        // 添加变更类型过滤条件
        if (StringUtils.isNotEmpty(queryDTO.getHistoryType())) {
            // 这里需要根据详情表中的type字段进行过滤，但由于是关联查询，暂时在Service层处理
            filter = filter.andEq("type", queryDTO.getHistoryType());
        }
        if (queryDTO.getApproveTimeStart() != null) {
            filter = filter.andGe("approveTime", String.valueOf(queryDTO.getApproveTimeStart()));
        }

        if (queryDTO.getApproveTimeEnd() != null) {
            filter = filter.andLe("approveTime", String.valueOf(queryDTO.getApproveTimeEnd()));
        }

        return DataQuery.identifier(ProjectHistoryDetail.identifier)
                .limit(queryDTO.getPageSize(), queryDTO.getPageNo())
                .filter(filter, ProjectHistoryDetail.class);
    }
}
