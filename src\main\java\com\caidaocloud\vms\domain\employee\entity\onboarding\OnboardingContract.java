package com.caidaocloud.vms.domain.employee.entity.onboarding;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OnboardingContract extends BaseEntity {

    private String empId;

    private DictSimple contractType;

    private String contractNo;

    private Integer contractPeriod;

    private Long startDate;

    private Long endDate;

    private EnumSimple probationPeriod; // TODO: implement enum

    private Long probationPeriodBeginDate;

    private Long probationPeriodEndDate;

    private EnumSimple probation; // TODO: implement enum

    private EnumSimple periodType; // TODO: implement enum

    private EnumSimple signType; // TODO: implement enum

    private String contractTypeSet;

    private String contractTypeSetTxt;

    private Double contractYear;

    public static String identifier = "entity.vms.OnboardingContract";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

}