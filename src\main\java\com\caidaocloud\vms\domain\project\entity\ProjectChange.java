package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 字段变更记录
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectChange {
    private String fieldName;
    private Object oldValue;
    private Object newValue;

    private HistoryType historyType;
    private HistoryType subType;
    private OperationType operationType;

    /**
     * 构造函数用于字段级别的变更
     */
    public ProjectChange(String fieldName, Object oldValue, Object newValue) {
        this.fieldName = fieldName;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.operationType = OperationType.UPDATE;
    }

    /**
     * 构造函数用于实体级别的变更（新增、删除等）
     */
    public ProjectChange(String description, OperationType operationType) {
        this.fieldName = description;
        this.operationType = operationType;
    }
}