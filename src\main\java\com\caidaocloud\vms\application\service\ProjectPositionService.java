package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.application.event.ProjectBudgetRefreshEvent;
import com.caidaocloud.vms.application.service.emp.PostService;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.OrganizeService;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.repository.*;
import org.springframework.beans.BeanUtils;

import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.PositionRequirementDto;
import com.caidaocloud.vms.application.dto.PositionSupplierDto;
import com.caidaocloud.vms.application.dto.PositionSupplierQueryDto;
import com.caidaocloud.vms.application.dto.ProjectPositionCreateDto;
import com.caidaocloud.vms.application.dto.ProjectPositionEditDto;
import com.caidaocloud.vms.application.dto.ProjectPositionQueryDTO;
import com.caidaocloud.vms.application.vo.PositionRequirementVO;
import com.caidaocloud.vms.application.vo.PositionSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectPositionPageVO;
import com.caidaocloud.vms.domain.project.entity.PositionRequirement;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.enums.DurationType;
import com.caidaocloud.vms.domain.project.enums.InviteStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Option;
import com.caidaocloud.vms.domain.project.dto.FormatAllResult;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 项目岗位服务
 *
 * <AUTHOR> Zhou
 * @date 2025/9/22
 */
@Service
@Slf4j
public class ProjectPositionService {
    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectPositionRepository projectPositionRepository;

    @Autowired
    private PositionRequirementRepository positionRequirementRepository;

    @Autowired
    private PositionSupplierRepository positionSupplierRepository;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private PostService postService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private OrganizeService organizeService;
    @Autowired
    private SupplierService supplierService;

    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ProjectHistoryFactory projectHistoryFactory;

    @Autowired
    private ProjectHistoryService projectHistoryService;

    @Autowired
    private ProjectSettingService projectSettingService;

    /**
     * 新增项目岗位
     * 传入岗位id、组织id以及公司id
     *
     * @param dto 岗位信息
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = {
            ProjectPositionCreateDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.CREATE)
    public void savePosition(ProjectPositionCreateDto dto) {
        Project project = projectRepository.getById(dto.getProjectId());
        if (project == null) {
            throw new ServerException("Project not found: " + dto.getProjectId());
        }
        // TODO: 2025/9/23 是否校验岗位重复

        ProjectPosition position = project.createPosition(dto.getPosition(), dto.getCompany(),
                dto.getOrganization());

        // 保存岗位
        projectPositionRepository.saveOrUpdate(position);
        positionRequirementRepository.init(position.getPositionRequirement(), position.getBid());
    }

    /**
     * 编辑项目岗位基本信息
     *
     * @param positionDto 岗位信息
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = {
            ProjectPositionEditDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.UPDATE)
    public void editPosition(ProjectPositionEditDto positionDto) {
        if (positionDto.getBid() == null) {
            throw new ServerException("岗位ID不能为空");
        }
        // TODO: 2025/9/23 审批中不能修改

        Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionDto.getBid());
        if (!positionOpt.isPresent()) {
            throw new ServerException("岗位不存在");
        }

        ProjectPosition position = positionOpt.get();
        position.checkUpdate();
        // TODO: 2025/11/6 校验项目预算总额 
        // TODO: 2025/11/6 统计岗位在职员工数量 

        position.updateBasicInfo(positionDto);

        // 保存更新
        projectPositionRepository.saveOrUpdate(position);
    }

    /**
     * 获取项目岗位列表
     *
     * @param projectId 项目ID
     * @return 岗位列表
     */
    public List<ProjectPositionVO> getPositionList(String projectId) {
        List<ProjectPosition> positions = projectPositionRepository.loadPositionList(projectId);

        return positions.stream().map(position -> {
            ProjectPositionVO vo = new ProjectPositionVO();
            BeanUtils.copyProperties(position, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取岗位详情
     *
     * @param positionId 岗位ID
     * @return 岗位详情
     */
    public ProjectPositionVO getPositionDetail(String positionId) {
        Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
        if (!positionOpt.isPresent()) {
            throw new ServerException("岗位不存在");
        }

        ProjectPosition position = positionOpt.get();
        ProjectPositionVO vo = new ProjectPositionVO();
        BeanUtils.copyProperties(position, vo);
        vo.setDurationType(DurationType.fromValue(position.getDurationType()));
        vo.setEmploymentType(position.getEmploymentType().getValue());
        return vo;
    }

    /**
     * 删除项目岗位
     *
     * @param dto 岗位dto
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = {
            ProjectPositionEditDto.class }, historyType = HistoryType.POSITION, operationType = OperationType.DELETE)
    public void deletePosition(ProjectPositionEditDto dto) {
        Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(dto.getBid());
        if (!positionOpt.isPresent()) {
            throw new ServerException("岗位不存在");
        }
        // // 删除岗位相关的招聘要求
        // positionRequirementRepository.deleteByPositionId(positionId);
        //
        // // 删除岗位相关的供应商关系
        // positionSupplierRepository.deleteByPositionId(positionId);

        // 删除岗位
        projectPositionRepository.deletePosition(positionOpt.get());
    }

    // ==================== 岗位招聘要求相关方法 ====================

    /**
     * 保存或更新岗位招聘要求
     *
     * @param requirementDto 招聘要求信息
     */
    @PaasTransactional
    public void editRequirement(PositionRequirementDto requirementDto) {
        Optional<PositionRequirement> optional = positionRequirementRepository
                .getByPositionId(requirementDto.getPositionId());
        if (!optional.isPresent()) {
            throw new ServerException("PositionRequirement not found: " + requirementDto.getPositionId());
        }
        PositionRequirement requirement = optional.get();
        requirement.update(requirementDto);

        // 保存要求
        positionRequirementRepository.saveOrUpdate(requirement);
    }

    /**
     * 获取岗位招聘要求
     *
     * @param positionId 岗位ID
     * @return 招聘要求
     */
    public PositionRequirementVO getPositionRequirement(String positionId) {
        Optional<PositionRequirement> requirementOpt = positionRequirementRepository.getByPositionId(positionId);
        if (!requirementOpt.isPresent()) {
            return null;
        }

        PositionRequirement requirement = requirementOpt.get();
        PositionRequirementVO vo = new PositionRequirementVO();
        BeanUtils.copyProperties(requirement, vo);
        return vo;
    }

    // ==================== 岗位供应商关系相关方法 ====================

    /**
     * 添加岗位供应商关系
     *
     * @param supplierDto 供应商关系信息
     */
    @PaasTransactional
    public void addPositionSupplier(PositionSupplierDto supplierDto) {
        // 检查关系是否已存在
        if (positionSupplierRepository.existsByPositionIdAndSupplierId(
                supplierDto.getPositionId(), supplierDto.getSupplierId())) {
            throw new ServerException("该岗位已关联此供应商");
        }

        // 创建新的关系
        PositionSupplier positionSupplier = new PositionSupplier(
                supplierDto.getProjectId(),
                supplierDto.getPositionId(),
                supplierDto.getSupplierId(),
                supplierDto.getSupplierContact(),
                supplierDto.getQuotationMode(),
                supplierDto.getQuotationValue());

        positionSupplierRepository.saveOrUpdate(positionSupplier);
    }

    /**
     * 获取岗位的供应商列表（支持查询条件）
     *
     * @param queryDto 查询条件
     * @return 供应商列表
     */
    public List<PositionSupplierVO> getPositionSuppliers(PositionSupplierQueryDto queryDto) {
        List<PositionSupplier> relations = positionSupplierRepository.getByQuery(queryDto);

        if (relations.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询供应商信息
        List<String> supplierIds = relations.stream()
                .map(PositionSupplier::getSupplierId)
                .distinct()
                .collect(Collectors.toList());

        List<Supplier> suppliers = supplierRepository.list(supplierIds);
        Map<String, Supplier> supplierMap = suppliers.stream()
                .collect(Collectors.toMap(Supplier::getBid, supplier -> supplier));

        // 批量查询供应商联系人信息
        List<SupplierContact> contactList = supplierService.loadContact(Sequences.sequence(relations)
                .flatMap(PositionSupplier::getSupplierContact).toList());

        // 组装VO并应用供应商名称过滤
        List<PositionSupplierVO> result = relations.stream()
                .map(relation -> {
                    PositionSupplierVO vo = ObjectConverter.convert(relation, PositionSupplierVO.class);

                    // 设置供应商信息
                    Supplier supplier = supplierMap.get(relation.getSupplierId());
                    if (supplier != null) {
                        vo.setSupplierCode(supplier.getSupplierCode());
                        vo.setSupplierName(supplier.getSupplierName());
                    }

                    // 设置供应商联系人信息
                    if (CollectionUtils.isNotEmpty(relation.getSupplierContact())) {
                        List<String> contactNameList = new ArrayList<>();
                        for (String id : relation.getSupplierContact()) {
                            Option<SupplierContact> option = Sequences.sequence(contactList)
                                    .find(c -> relation.getSupplierContact().equals(c.getBid()));
                            if (option.isDefined()) {
                                SupplierContact contact = option.get();
                                contactNameList.add(contact.getContact());
                            }
                        }

                        vo.setSupplierContactName(StringUtils.join(contactNameList, ","));
                    }

                    vo.setQuotationMode(QuotationMode.fromValue(relation.getQuotationMode()));
                    return vo;
                })
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 获取岗位的供应商列表（简化版本，保持向后兼容）
     *
     * @param positionId 岗位ID
     * @return 供应商列表
     */
    public List<PositionSupplierVO> getPositionSuppliers(String positionId) {
        PositionSupplierQueryDto queryDto = new PositionSupplierQueryDto();
        queryDto.setPositionId(positionId);
        return getPositionSuppliers(queryDto);
    }

    /**
     * 删除岗位供应商关系
     *
     * @param relationId 关系ID
     */
    @PaasTransactional
    public void removePositionSupplier(String relationId) {
        Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(relationId);
        if (!relationOpt.isPresent()) {
            throw new ServerException("岗位供应商关系不存在");
        }
        positionSupplierRepository.deleteRelation(relationOpt.get());
    }

    /**
     * 停用岗位供应商关系，将应邀状态改为终止
     *
     * @param relationId 关系ID
     */
    @PaasTransactional
    public void terminatePositionSupplier(String relationId) {
        Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(relationId);
        if (!relationOpt.isPresent()) {
            throw new ServerException("岗位供应商关系不存在");
        }

        PositionSupplier relation = relationOpt.get();
        relation.terminate(); // 调用实体方法将状态改为终止
        positionSupplierRepository.saveOrUpdate(relation);
    }

    /**
     * 更新岗位供应商报价信息
     *
     * @param supplierDto 供应商报价信息
     */
    @PaasTransactional
    public void updatePositionSupplier(PositionSupplierDto supplierDto) {
        if (supplierDto.getBid() == null) {
            throw new ServerException("关系ID不能为空");
        }

        Optional<PositionSupplier> relationOpt = positionSupplierRepository.getById(supplierDto.getBid());
        if (!relationOpt.isPresent()) {
            throw new ServerException("岗位供应商关系不存在");
        }

        PositionSupplier relation = relationOpt.get();
        relation.update(supplierDto);

        positionSupplierRepository.saveOrUpdate(relation);
    }

    public PageResult<ProjectPositionPageVO> getPositionPage(ProjectPositionQueryDTO queryDTO) {
        PageResult<ProjectPosition> result = projectPositionRepository.pagePosition(queryDTO.getProjectId(),
                queryDTO.getPositionName(), queryDTO.getPageSize(), queryDTO.getPageNo());
        if (result.getItems().isEmpty()) {
            return new PageResult<>();
        }

        // 收集需要查询的ID列表
        List<String> positionIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getPosition).toList();
        List<String> companyIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getCompany).toList();
        List<String> organizationIds = Sequences.sequence(result.getItems()).map(ProjectPosition::getOrganization)
                .toList();

        // 批量查询关联信息
        List<PostInfoDto> postList = postService.loadPostList(positionIds);
        List<CompanyInfoDto> companyList = companyService.loadCompanyList(companyIds);
        List<OrgInfoDto> orgList = organizeService.loadOrgList(organizationIds);

        // 组装VO
        List<ProjectPositionPageVO> list = Sequences.sequence(result.getItems()).map(data -> {
            ProjectPositionPageVO vo = ObjectConverter.convert(data, ProjectPositionPageVO.class);

            // 组装岗位信息
            Option<PostInfoDto> postOpt = Sequences.sequence(postList)
                    .find(post -> data.getPosition().equals(post.getBid()));
            if (postOpt.isDefined()) {
                vo.setPositionTxt(postOpt.get().getName());
                vo.setPositionCode(postOpt.get().getCode());
                vo.setJobTxt(postOpt.get().getJobName());
            }

            // 组装公司信息
            Option<CompanyInfoDto> companyOpt = Sequences.sequence(companyList)
                    .find(company -> data.getCompany().equals(company.getBid()));
            if (companyOpt.isDefined()) {
                vo.setCompanyTxt(companyOpt.get().getCompanyName());
            }

            // 组装组织信息
            Option<OrgInfoDto> orgOpt = Sequences.sequence(orgList)
                    .find(org -> data.getOrganization().equals(org.getBid()));
            if (orgOpt.isDefined()) {
                vo.setOrganizationTxt(orgOpt.get().getOrgName());
            }

            return vo;
        }).toList();
        return new PageResult<>(list, result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    /**
     * 提交项目岗位变更
     * 根据draft生成change，保存historyDetail，发起工作流
     * 无分布式事务，手动回滚
     *
     * @param projectId 项目ID
     */
    public void commitProjectPosition(String projectId) {
        // 获取项目信息
        Project project = projectRepository.getById(projectId);
        if (project == null) {
            throw new ServerException("Project not found: " + projectId);
        }

        List<ProjectPosition> supplierList = projectPositionRepository.loadPositionList(projectId);

        // 获取项目岗位相关的所有草稿
        List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.POSITION);
        if (drafts.isEmpty()) {
            log.info("No position drafts found for project: " + projectId);
            return;
        }

        ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(HistoryType.POSITION,
                supplierList, drafts);
        ProjectHistory history = new ProjectHistory(projectId);
        history.setDetailList(Lists.list(historyDetail));

        doCommit(history, WorkflowConfig.PROJECT_MANAGEMENT.getCode());

        new ProjectBudgetRefreshEvent(projectId).publish();

        // TODO: 2025/10/17
        // 清理草稿（可选，根据业务需求决定是否在此处清理）
        // for (ProjectDraft draft : drafts) {
        // projectDraftRepository.deleteByTargetId(draft.getTargetId());
        // }
    }

    private void doCommit(ProjectHistory history, String funCode) {
        try {
            projectHistoryService.saveHistory(history);
            workflowService.startWorkflow(history, funCode);
        } catch (WorkflowStartException e) {
            // 工作流启动失败，手动回滚已保存的历史记录
            projectHistoryService.rollbackHistory(history);
            throw new ServerException("Failed to start workflow ", e);
        } catch (Exception e) {
            throw new ServerException("Failed to commit ", e);
        }
    }

    public void commitProjectPositionIndividually(String projectId) {
        commitProjectPositionIndividually(projectId, null);
        new ProjectBudgetRefreshEvent(projectId).publish();
    }

    // @PaasTransactional
    public void commitProjectPositionIndividually(String projectId, String positionId) {
        // 获取项目信息
        Project project = projectRepository.getById(projectId);
        if (project == null) {
            throw new ServerException("Project not found: " + projectId);
        }
        List<ProjectPosition> commitList;
        if (StringUtils.isEmpty(positionId)) {
            commitList = projectPositionRepository.loadPositionList(projectId);
        } else {
            Optional<ProjectPosition> optional = projectPositionRepository.getPosition(positionId);
            if (!optional.isPresent()) {
            }
            commitList = Lists.list(optional.get());
        }

        batchCommitPosition(project, commitList);
    }

    private void batchCommitPosition(Project project, List<ProjectPosition> commitList) {
        List<String> positionIds = Sequences.sequence(commitList).map(AbstractData::getBid).toList();
        List<ProjectDraft> draftList = projectDraftRepository.listByPositionIds(positionIds);
        for (ProjectPosition projectPosition : commitList) {
            Option<ProjectDraft> option = Sequences.sequence(draftList)
                    .find(d -> d.getTargetId().equals(projectPosition.getBid()));
            if (option.isEmpty()) {
                throw new ServerException("Position draft not found: " + projectPosition.getBid());
            }
            // todo 加载所有entity
            FormatAllResult result = projectPosition.formatAll(option.get(), draftList);
            String snapshot = result.getSnapshot();
            List<ProjectChange> changes = result.getChanges();
            ProjectHistoryDetail detail = new ProjectHistoryDetail(HistoryType.POSITION);
            detail.setSnapshot(snapshot);
            detail.setChange(changes);
            detail.setDraft(result.getDraftIdList());
            ProjectHistory history = new ProjectHistory(project.getBid(), detail);
            doCommit(history, WorkflowConfig.PROJECT_POSITION.getCode());
        }
    }

    @PaasTransactional
    public void publish(String positionId) {

        Optional<ProjectPosition> positionOpt = projectPositionRepository.getPosition(positionId);
        if (!positionOpt.isPresent()) {
            throw new ServerException("岗位不存在");
        }
        ProjectPosition position = positionOpt.get();
        if (position.getStatus() != PositionStatus.APPROVED) {
            throw new ServerException("岗位不能发布");
        }

        List<PositionSupplier> supplierList = positionSupplierRepository.getByPositionId(positionId);
        for (PositionSupplier supplier : supplierList) {
            if (supplier.getInviteStatus() == null) {
                supplier.publish();
                positionSupplierRepository.saveOrUpdate(supplier);
            }
        }
        position.publish();
        projectPositionRepository.saveOrUpdate(position);
    }

    @PaasTransactional
    public void supplierPublish(String positionSupplierBid) {
        Optional<PositionSupplier> positionSupplier = positionSupplierRepository.getById(positionSupplierBid);
        if (!positionSupplier.isPresent()) {
            throw new ServerException("岗位供应商关系不存在");
        }
        PositionSupplier ps = positionSupplier.get();
        if (ps.getInviteStatus() != null) {
            throw new ServerException("岗位不能发布");
        }
        ps.publish();
        positionSupplierRepository.saveOrUpdate(ps);
    }
}
