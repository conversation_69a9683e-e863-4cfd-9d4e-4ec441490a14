package com.caidaocloud.vms.domain.supplier.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;

public interface SupplierContactRepository {

	void deleteContact(String contactId);

	String saveOrUpdate(SupplierContact supplierContact);

	Optional<SupplierContact> getContact(String bid);

	List<SupplierContact> loadContactListBySuppliers(String supplierId);

	List<SupplierContact> loadContactListBySuppliers(List<String> supplierIds);

	List<SupplierContact> loadContactList(List<String> contactIds);

	/**
	 * 根据创建人ID查找供应商联系人
	 * 
	 * @param createBy 创建人ID
	 * @return 供应商联系人列表
	 */
	List<SupplierContact> findByCreateBy(String createBy);
}