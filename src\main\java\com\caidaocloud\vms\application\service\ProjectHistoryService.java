package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectHistoryDetailVO;
import com.caidaocloud.vms.application.vo.ProjectHistoryPageVO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryDetailRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectHistoryRepository;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史服务类
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Service
public class ProjectHistoryService {

	@Autowired
	private ProjectHistoryRepository projectHistoryRepository;
	@Autowired
	private ProjectHistoryDetailRepository projectHistoryDetailRepository;

	// /**
	// * 保存项目历史记录并启动工作流
	// *
	// * @param projectId 项目历史记录
	// * @param projectHistory
	// * @param projectHistoryDetails
	// * @param historyType
	// * @param operationType
	// * @param workflowEnabled
	// * @param postWorkflowEnabled
	// */
	// public void createAndStartWorkflow(String projectId, ProjectHistory
	// projectHistory, List<ProjectHistoryDetail> projectHistoryDetails, HistoryType
	// historyType, boolean workflowEnabled, boolean postWorkflowEnabled) {
	// // 保存历史记录
	// List<PositionHistoryDetail> positionHistoryDetailList =
	// Sequences.sequence(projectHistoryDetails)
	// .filter(d -> d instanceof PositionHistoryDetail).map(d ->
	// (PositionHistoryDetail) d).toList();
	// List<ProjectHistoryDetail> projectHistoryDetailList =
	// Sequences.sequence(projectHistoryDetails)
	// .filter(d -> !positionHistoryDetailList.contains(d)).toList();
	// projectHistoryRepository.saveOrUpdate(projectHistory);
	// // TODO: 2025/10/9 保存detail,将同一个类型的整合在一起。若岗位单独审批，将position单独保存并发起流程
	//
	// // TODO: 启动工作流审批流程
	// if (workflowEnabled) {
	// // startWorkflowProcess(projectId);
	// }
	// }

	/**
	 * 审批通过后的处理
	 *
	 * @param historyId 历史记录ID
	 */
	public void approveHistory(String historyId) {
		// TODO: 实现审批通过后的逻辑
		// 1. 更新历史记录状态
		// 2. 将历史记录中的数据应用到实际实体
		// 3. 发送通知
	}

	/**
	 * 审批拒绝后的处理
	 *
	 * @param historyId 历史记录ID
	 * @param reason    拒绝原因
	 */
	public void rejectHistory(String historyId, String reason) {
		// TODO: 实现审批拒绝后的逻辑
		// 1. 更新历史记录状态
		// 2. 记录拒绝原因
		// 3. 发送通知
	}

	/**
	 * 获取当前用户暂存的修改记录
	 *
	 * @param projectId
	 * @return
	 */
	public Optional<ProjectHistory> loadUserTempHistory(String projectId) {
		return projectHistoryRepository.loadTempByProjectIdAndUser(projectId);
	}

	public void saveDetail(ProjectHistoryDetail detail) {
		projectHistoryDetailRepository.saveOrUpdate(detail);
	}

	public void saveHistory(ProjectHistory history) {
		projectHistoryRepository.saveOrUpdate(history);
	}

	public void rollbackHistory(ProjectHistory projectHistory) {
		projectHistoryRepository.rollbackHistory(projectHistory);
	}

	public Optional<ProjectHistory> loadByBusinessKey(String businessKey) {
		String bid = StringUtils.substringAfterLast(businessKey, "_");
		return projectHistoryRepository.getById(bid);
	}

	public List<ProjectHistoryDetail> loadDetail(String historyId) {
		return projectHistoryDetailRepository.getByHistoryId(historyId);
	}

	/**
	 * 分页查询审批通过的历史记录
	 *
	 * @param queryDTO 查询条件
	 * @return 分页结果
	 */
	public PageResult<ProjectHistoryPageVO> findApprovedByPage(ProjectHistoryQueryDTO queryDTO) {
		PageResult<ProjectHistoryDetail> pageResult = projectHistoryDetailRepository.findApprovedByPage(queryDTO);

		// 转换为VO
		List<ProjectHistoryPageVO> voList = Sequences.sequence(pageResult.getItems())
				.map(this::convertToPageVO)
				.toList();

		PageResult<ProjectHistoryPageVO> result = new PageResult<>();
		result.setItems(voList);
		result.setTotal(pageResult.getTotal());
		result.setPageNo(pageResult.getPageNo());
		result.setPageSize(pageResult.getPageSize());

		return result;
	}

	/**
	 * 获取历史记录详情
	 *
	 * @param detailId 历史记录ID
	 * @return 历史记录详情
	 */
	public ProjectHistoryDetailVO getHistoryDetail(String detailId) {
		Optional<ProjectHistoryDetail> historyOpt = projectHistoryDetailRepository.getById(detailId);
		if (!historyOpt.isPresent()) {
			throw new ServerException("数据不存在");
		}

		return ObjectConverter.convert(historyOpt.get(), ProjectHistoryDetailVO.class);
	}

	/**
	 * 转换为分页VO
	 */
	private ProjectHistoryPageVO convertToPageVO(ProjectHistoryDetail history) {
		ProjectHistoryPageVO vo = ObjectConverter.convert(history, ProjectHistoryPageVO.class);


		// 生成变更缩略信息
		vo.setChangeSummary(generateChangeSummary(history));

		// TODO: 设置提交人和审批人信息（需要调用员工服务）

		return vo;
	}

	/**
	 * 转换为详情VO
	 */
	private ProjectHistoryDetailVO convertToDetailVO(ProjectHistoryDetail history) {
		ProjectHistoryDetailVO vo = ObjectConverter.convert(history, ProjectHistoryDetailVO.class);



		// TODO: 设置提交人和审批人信息（需要调用员工服务）

		return vo;
	}

	/**
	 * 生成变更缩略信息
	 */
	private String generateChangeSummary(ProjectHistoryDetail detail) {
// TODO: 2025/10/23 生成变更信息
		// 统计各类型的变更数量
		StringBuilder summary = new StringBuilder();
				HistoryType type = HistoryType.fromValue(detail.getType().getValue());
				int changeCount = detail.getChange() != null ? detail.getChange().size() : 0;
				if (summary.length() > 0) {
					summary.append("，");
				}
				summary.append(type.getDisplay()).append("(").append(changeCount).append("项)");

		return summary.toString();
	}

}
