package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectPositionCreateDto;
import com.caidaocloud.vms.application.dto.ProjectPositionEditDto;
import com.caidaocloud.vms.application.dto.ProjectPositionQueryDTO;
import com.caidaocloud.web.Result;
import com.caidaocloud.vms.application.dto.PositionRequirementDto;
import com.caidaocloud.vms.application.dto.PositionSupplierDto;
import com.caidaocloud.vms.application.dto.PositionSupplierQueryDto;
import com.caidaocloud.vms.application.service.ProjectPositionService;
import com.caidaocloud.vms.application.vo.PositionRequirementVO;
import com.caidaocloud.vms.application.vo.PositionSupplierVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectPositionPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目岗位控制器
 * 
 * <AUTHOR> Zhou
 * @date 2025/9/22
 */
@RestController
@RequestMapping("/api/vms/v1/manager/project/position")
@Api(tags = "项目岗位管理")
public class ProjectPositionController {

    @Autowired
    private ProjectPositionService projectPositionService;

    /**
     * 新增项目岗位
     * 传入岗位id、组织id以及公司id
     */
    @PostMapping("/save")
    @ApiOperation("新增项目岗位")
    public Result<Void> savePosition(@RequestBody ProjectPositionCreateDto dto) {
        projectPositionService.savePosition(dto);
        return Result.success();
    }

    /**
     * 编辑项目岗位基本信息
     */
    @PostMapping("/edit")
    @ApiOperation("编辑项目岗位基本信息")
    public Result<Void> editPosition(@RequestBody ProjectPositionEditDto positionDto) {
        projectPositionService.editPosition(positionDto);
        return Result.success();
    }

    /**
     * 获取项目岗位列表
     * 列表中展示岗位的基本信息
     */
    @GetMapping("/list")
    @ApiOperation("获取项目岗位列表")
    public Result<List<ProjectPositionVO>> getPositionList(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {
        List<ProjectPositionVO> positions = projectPositionService.getPositionList(projectId);
        return Result.ok(positions);
    }

    /**
     * 获取项目岗位列表
     * 列表中展示岗位的基本信息
     */
    @PostMapping("/page")
    @ApiOperation("获取项目岗位列表")
    public Result<PageResult<ProjectPositionPageVO>> getPositionList(
            @RequestBody ProjectPositionQueryDTO queryDTO) {
        PageResult<ProjectPositionPageVO> positions = projectPositionService.getPositionPage(queryDTO);
        return Result.ok(positions);
    }

    /**
     * 获取岗位详情
     */
    @GetMapping("/detail")
    @ApiOperation("获取岗位详情")
    public Result<ProjectPositionVO> getPositionDetail(
            @ApiParam(value = "岗位ID", required = true) @RequestParam String positionId) {
        ProjectPositionVO position = projectPositionService.getPositionDetail(positionId);
        return Result.ok(position);
    }

    /**
     * 删除项目岗位
     */
    @PostMapping("/delete")
    @ApiOperation("删除项目岗位")
    public Result<Void> deletePosition(
            @RequestBody ProjectPositionEditDto dto) {
        projectPositionService.deletePosition(dto);
        return Result.success();
    }

    /**
     * 提交项目岗位变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit")
    @ApiOperation(value = "提交项目岗位变更", notes = "提交项目岗位的所有变更，生成历史记录并发起工作流")
    public Result<Void> commitProjectPosition(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId) {

        projectPositionService.commitProjectPosition(projectId);
        return Result.success();
    }

    /**
     * 单独提交项目岗位变更
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/commit/indiv")
    @ApiOperation(value = "提交项目岗位变更", notes = "提交项目岗位的所有变更，生成历史记录并发起工作流")
    public Result<Void> commitProjectPositionIndividually(
            @ApiParam(value = "项目ID", required = true) @RequestParam String projectId,String positionId) {

        projectPositionService.commitProjectPositionIndividually(projectId, positionId);
        return Result.success();
    }

    // ==================== 岗位招聘要求管理 ====================

    /**
     * 保存或更新岗位招聘要求
     */
    @PostMapping("/requirement/edit")
    @ApiOperation("编辑岗位招聘要求")
    public Result<Void> saveOrUpdateRequirement(@RequestBody PositionRequirementDto requirementDto) {
        projectPositionService.editRequirement(requirementDto);
        return Result.success();
    }

    /**
     * 获取岗位招聘要求
     */
    @GetMapping("/requirement/detail")
    @ApiOperation("获取岗位招聘要求")
    public Result<PositionRequirementVO> getPositionRequirement(
            @ApiParam(value = "岗位ID", required = true) @RequestParam String positionId) {
        PositionRequirementVO requirement = projectPositionService.getPositionRequirement(positionId);
        return Result.ok(requirement);
    }

    // ==================== 岗位供应商关系管理 ====================

    /**
     * 添加岗位供应商关系
     */
    @PostMapping("/supplier/add")
    @ApiOperation("添加岗位供应商关系")
    public Result<Void> addPositionSupplier(@RequestBody PositionSupplierDto supplierDto) {
        projectPositionService.addPositionSupplier(supplierDto);
        return Result.success();
    }

    /**
     * 更新岗位供应商报价信息
     */
    @PostMapping("/supplier/edit")
    @ApiOperation("更新岗位供应商报价信息")
    public Result<Void> updateSupplierQuotation(@RequestBody PositionSupplierDto supplierDto) {
        projectPositionService.updatePositionSupplier(supplierDto);
        return Result.success();
    }

    /**
     * 获取岗位的供应商列表（支持模糊查询供应商名称和应邀状态）
     */
    @PostMapping("/supplier/list")
    @ApiOperation("获取岗位的供应商列表")
    public Result<List<PositionSupplierVO>> getPositionSuppliers(
            @ApiParam(value = "查询条件", required = true) @RequestBody PositionSupplierQueryDto queryDto) {

        List<PositionSupplierVO> suppliers = projectPositionService.getPositionSuppliers(queryDto);
        return Result.ok(suppliers);
    }

    /**
     * 删除岗位供应商关系
     */
    @PostMapping("/supplier/remove")
    @ApiOperation("删除岗位供应商关系")
    public Result<Void> removePositionSupplier(
            @ApiParam(value = "关系ID", required = true) @RequestParam String relationId) {
        projectPositionService.removePositionSupplier(relationId);
        return Result.success();
    }

    /**
     * 停用岗位供应商关系，将应邀状态改为终止
     */
    @PostMapping("/supplier/terminate")
    @ApiOperation("停用岗位供应商关系")
    public Result<Void> terminatePositionSupplier(
            @ApiParam(value = "关系ID", required = true) @RequestParam String relationId) {
        projectPositionService.terminatePositionSupplier(relationId);
        return Result.success();
    }

}