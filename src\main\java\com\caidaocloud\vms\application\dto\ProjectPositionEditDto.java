package com.caidaocloud.vms.application.dto;

import com.caidaocloud.vms.domain.project.enums.DurationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "项目岗位信息")
public class ProjectPositionEditDto {

    @ApiModelProperty(value = "岗位ID")
    private String bid;

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "公司ID", required = true)
    private String company;

    @ApiModelProperty(value = "开始日期(时间戳)")
    private Long startDate;

    @ApiModelProperty(value = "结束日期(时间戳)")
    private Long endDate;

    @ApiModelProperty(value = "持续时间类型")
    private DurationType durationType;

    @ApiModelProperty(value = "持续时间(天)")
    private Integer duration;

    @ApiModelProperty(value = "总预算")
    private Integer totalBudget;

    @ApiModelProperty(value = "计划人数")
    private Integer plannedHeadcount;

    @ApiModelProperty(value = "工作地点")
    private String workplace;

    @ApiModelProperty(value = "联系人员工ID")
    private String contactEmpId;

    @ApiModelProperty(value = "用工类型")
    private String employmentType;
}
