package com.caidaocloud.vms.domain.base.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.base.EmpInfoDto;
import com.caidaocloud.vms.application.service.emp.EmpService;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;
import com.caidaocloud.vms.infrastructure.feign.IWfOperateFeignClient;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作流服务类
 * 负责判断是否需要走工作流审批流程
 *
 * <AUTHOR> Zhou
 * @date 2025/9/25
 */
@Service
@Slf4j
public class WorkflowService {

    @Autowired
    private IWfOperateFeignClient wfOperateFeignClient;

    @Autowired
    private IWfRegisterFeign wfRegisterFeign;

    @Autowired
    private EmpService empService;

    /**
     * 启动工作流流程
     *
     * @param history 项目历史记录
     */
    public void startWorkflow(ProjectHistory history) {
        // TODO: 2025/10/24 修改项目状态为审批中
        startWorkflow(history, WorkflowConfig.PROJECT_MANAGEMENT.getCode());
    }

    /**
     * 启动工作流流程
     *
     * @param history 项目历史记录
     * @param funCode funcode
     */
    public void startWorkflow(ProjectHistory history, String funCode) {
        log.info("WorkflowService: 启动工作流，  单据ID: {},funcode={}", history.getBid(), funCode);
        EmpInfoDto emp = empService.loadEmp(String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()));

        doWorkflow(history.getBid(), emp, funCode);
    }

    /**
     * 启动员工变更记录工作流流程
     *
     * @param changeRecord 员工变更记录
     * @param funCode      funcode
     */
    public void startWorkflow(EmployeeChangeRecord changeRecord, String funCode) {
        log.info("WorkflowService: 启动员工变更工作流，单据ID: {}, funcode={}", changeRecord.getBid(), funCode);
        EmpInfoDto emp = empService.loadEmp(String.valueOf(SecurityUserUtil.getSecurityUserInfo()
                .getEmpId()));

        doWorkflow(changeRecord.getBid(), emp, funCode);

    }

    private void doWorkflow(String businessId, EmpInfoDto emp, String funCode) {
        // 根据实体类型和历史记录类型确定工作流流程
        WfBeginWorkflowDto beginDto = new WfBeginWorkflowDto();
        beginDto.setFuncCode(funCode);
        beginDto.setBusinessId(businessId);
        beginDto.setApplicantId(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()));
        beginDto.setApplicantName(emp.getName());
        // 业务单据事件时间为离职日期
        beginDto.setEventTime(System.currentTimeMillis());
        log.info("项目流程信息：" + FastjsonUtil.toJson(beginDto));
        Result wfResult = null;
        try {
            wfResult = wfRegisterFeign.begin(beginDto);
        } catch (Exception e) {
            log.error("流程发起失败,err msg={}", e.getMessage());
            throw new WorkflowStartException( e);
            // TODO: 2025/10/17 定义workflow异常并抛出
        }
        log.info("workflow result={}", wfResult);
        if (null != wfResult && !wfResult.isSuccess()) {
            throw new WorkflowStartException(wfResult.getMsg());
        }
    }

    public boolean checkWorkflowEnable() {
        try {
            Result<Boolean> result = wfOperateFeignClient.checkDefEnabled(WorkflowConfig.PROJECT_MANAGEMENT.getCode());
            if (null == result || !result.isSuccess()) {
                return false;
            }
            return (Boolean) result.getData();
        } catch (Exception e) {
            log.error("check work flow enable occurs error,fun code={}",(WorkflowConfig.PROJECT_MANAGEMENT.getCode()), e);
            throw new ServerException("check work flow enable occurs error", e);
        }
    }

    /**
     * 注册工作流function和callback
     *
     * @param workflowConfig 工作流配置枚举
     */
    public void register(WorkflowConfig workflowConfig) {
        // 1. 先注册function
        registerFunction(workflowConfig);

        // 2. 再注册callback
        registerCallback(workflowConfig);
    }

    /**
     * 兼容旧版本的注册方法
     *
     * @param name 工作流名称
     * @param code 工作流代码
     */
    @Deprecated
    public void register(String name, String code) {
        try {
            WorkflowConfig config = WorkflowConfig.fromCode(code);
            register(config);
        } catch (IllegalArgumentException e) {
            // 如果枚举中没有找到对应配置，使用旧的注册方式
            log.warn("未找到工作流配置枚举，使用旧的注册方式: name={}, code={}", name, code);
            WfMetaFunDto dto = new WfMetaFunDto(name, code,
                    WfFunctionPageJumpType.RELATIVE_PATH, "",
                    "caidaocloud-vms-service",
                    "", "/api/vms/v1/project/detail", "", Lists.list());
            wfRegisterFeign.registerFunction(dto);
        }
    }

    /**
     * 注册工作流function
     *
     * @param workflowConfig 工作流配置
     */
    private void registerFunction(WorkflowConfig workflowConfig) {
        WfMetaFunDto dto = new WfMetaFunDto(
                workflowConfig.getName(),
                workflowConfig.getCode(),
                WfFunctionPageJumpType.RELATIVE_PATH,
                "",
                "caidaocloud-vms-service",
                "",
                workflowConfig.getDetailPath(),
                "",
                Lists.list());
        wfRegisterFeign.registerFunction(dto);
        log.info("注册工作流function成功: name={}, code={}", workflowConfig.getName(), workflowConfig.getCode());
    }

    /**
     * 注册工作流callback
     *
     * @param workflowConfig 工作流配置
     */
    private void registerCallback(WorkflowConfig workflowConfig) {
        for (WorkflowConfig.CallbackConfig callback : workflowConfig.getCallbacks()) {
            WfMetaCallbackDto dto = new WfMetaCallbackDto(
                    callback.getCallbackName(workflowConfig.getName()),
                    callback.getCallbackCode(),
                    Lists.list(workflowConfig.getCode()),
                    SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                    callback.getCallbackAddress(),
                    "caidaocloud-vms-service",
                    "",
                    WfCallbackTypeEnum.RELATIVE_PATH,
                    WfCallbackTimeTypeEnum.NOW);
            wfRegisterFeign.registerCallback(dto);
            log.info("注册工作流callback成功: name={}, code={}, action={}",
                    callback.getCallbackName(workflowConfig.getName()),
                    callback.getCallbackCode(),
                    callback.getAction());
        }
    }

}
