package com.caidaocloud.vms.domain.project.history;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 详细历史格式化抽象类
 * 适用于需要显示具体字段变更的实体（如基本信息、设置等）
 * 
 * <AUTHOR>
 * @date 2025/10/11
 */
@Slf4j
public abstract class DetailedHistoryFormat<T> extends DataSimpleHistoryFormat<T> {

    @Override
    public List<ProjectChange> format(T originData,ProjectDraft draft) {
        try {
            if (draft == null) {
                return new ArrayList<>();
            }
            // 获取草稿中的操作类型和历史类型
            OperationType operationType = OperationType.fromValue(draft.getOperation());
            HistoryType historyType = HistoryType.fromValue(draft.getType().getValue());
            HistoryType subType = HistoryType.fromValue(draft.getSubType().getValue());

            // 反序列化草稿中的实体数据
            T draftEntity = deserializeDraftEntity(draft.getSnapshot());
            T originEntity = deserializeDraftEntity(draft.getOrigin());

            // 生成详细的字段变更记录
            return generateDetailedChanges(originEntity, draftEntity, historyType,subType, operationType);
            
        } catch (Exception e) {
            log.error("生成历史格式化失败，targetId: {}", draft.getTargetId(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 生成详细的字段变更记录
     */
    private List<ProjectChange> generateDetailedChanges(T originalEntity, T draftEntity,
            HistoryType historyType, HistoryType subType, OperationType operationType) {
        List<ProjectChange> changes = new ArrayList<>();

        try {
            Class<?> clazz = draftEntity.getClass();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                Object currentValue = field.get(draftEntity);
                if (field.getType().isAssignableFrom(HistoryFormat.class) ||  field.getGenericType().getClass()
                        .isAssignableFrom(HistoryFormat.class)) {
                    continue;
                }
                Object originalValue = originalEntity != null ? field.get(originalEntity) : null;

                // TODO: 2025/10/24 新增是否特殊处理
                if (!Objects.equals(originalValue, currentValue)) {
                    ProjectChange change = new ProjectChange();
                    change.setFieldName(field.getName());
                    change.setOldValue(originalValue);
                    change.setNewValue(currentValue);
                    change.setHistoryType(historyType);
                    change.setSubType(subType);
                    change.setOperationType(operationType);
                    changes.add(change);
                }
            }
        } catch (Exception e) {
            log.error("生成变更记录失败", e);
        }

        return changes;
    }

}
