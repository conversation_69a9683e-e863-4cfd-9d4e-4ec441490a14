package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.ProjectSupplierDto;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.application.vo.ProjectSimpleVO;
import com.caidaocloud.vms.application.vo.ProjectSupplierVO;
import com.caidaocloud.vms.application.vo.SupplierPageVO;
import com.caidaocloud.vms.application.vo.SupplierSelectVO;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSupplierRepository;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.entity.SupplierContact;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectSupplierService {

    @Autowired
    private ProjectSupplierRepository projectSupplierRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ProjectHistoryFactory projectHistoryFactory;

    @Autowired
    private ProjectHistoryService projectHistoryService;
    @Autowired
    private ProjectSettingRepository projectSettingRepository;

    /**
     * 添加项目供应商关系
     * 
     * @param projectSupplierDto 项目供应商关系信息
     * @return 关系ID
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = ProjectSupplierDto.class, historyType = HistoryType.SUPPLIER, operationType = OperationType.CREATE)
    public String addProjectSupplier(ProjectSupplierDto projectSupplierDto) {
        // 验证项目是否存在
        Project project = projectRepository.getById(projectSupplierDto.getProjectId());
        if (project == null) {
            throw new ServerException("Project not found: " + projectSupplierDto.getProjectId());
        }

        // 验证供应商是否存在
        Supplier supplier = supplierRepository.getById(projectSupplierDto.getSupplierId());
        if (supplier == null) {
            throw new ServerException("Supplier not found: " + projectSupplierDto.getSupplierId());
        }

        // 检查是否已存在相同的项目供应商关系
        if (projectSupplierRepository.existsByProjectIdAndSupplierId(
                projectSupplierDto.getProjectId(), projectSupplierDto.getSupplierId())) {
            throw new ServerException("Project supplier relationship already exists");
        }

        // 创建项目供应商关系
        ProjectSupplier projectSupplier = new ProjectSupplier(
                projectSupplierDto.getProjectId(),
                projectSupplierDto.getSupplierId());

        // 保存项目供应商关系
        return projectSupplierRepository.saveOrUpdate(projectSupplier);
    }

    /**
     * 删除项目供应商关系
     * 
     * @param id 关系ID
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = ProjectSupplierDto.class, historyType = HistoryType.SUPPLIER, operationType = OperationType.DELETE)
    public void deleteProjectSupplier(ProjectSupplierDto dto) {
        String id = dto.getBid();

        // 验证关系是否存在
        ProjectSupplier projectSupplier = projectSupplierRepository.getById(id);
        if (projectSupplier == null) {
            throw new ServerException("Project supplier relationship not found: " + id);
        }

        // 删除关系
        projectSupplierRepository.delete(projectSupplier);
    }

    /**
     * 获取项目供应商列表
     * 
     * @param queryDTO 查询条件
     * @return 供应商列表
     */
    public PageResult<ProjectSupplierVO> getProjectSuppliers(ProjectSupplierQueryDTO queryDTO) {
        // 分页查询项目供应商关系
        PageResult<ProjectSupplier> pageResult = projectSupplierRepository.findByPage(
                queryDTO.getProjectId(), queryDTO.getSupplierName(), queryDTO);
        if (pageResult.getItems().isEmpty()) {
            return new PageResult<>();
        }

        List<SupplierPageVO> supplier = supplierService.listSupplier(Sequences.sequence(pageResult.getItems())
                .map(ProjectSupplier::getSupplierId).toList());

        Map<String, List<SupplierContact>> supplierContactMap = supplierService
                .loadSupplierLatestContact(Sequences.sequence(supplier)
                        .map(SupplierPageVO::getBid).toList());
        // 转换为VO
        List<ProjectSupplierVO> voList = Sequences.sequence(pageResult.getItems()).map(s -> {
            ProjectSupplierVO vo = Sequences.sequence(supplier).find(ss -> s.getSupplierId().equals(ss.getBid()))
                    .map(ss -> ObjectConverter.convert(ss, ProjectSupplierVO.class)).getOrElse(new ProjectSupplierVO());
            vo.setBid(s.getBid());
            vo.setSupplierId(s.getSupplierId());
            List<SupplierContact> list = supplierContactMap.get(s.getSupplierId());
            if (CollectionUtils.isNotEmpty(list)) {
                vo.setSupplierContactName(list.get(0).getContact());
            }
            return vo;
        }).toList();
        return new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    /**
     * 提交项目供应商变更
     * 根据draft生成change，保存historyDetail，发起工作流
     * 无分布式事务，手动回滚
     *
     * @param projectId 项目ID
     */
    public void commitProjectSupplier(String projectId) {
        // 获取项目信息
        Project project = projectRepository.getById(projectId);
        if (project == null) {
            throw new ServerException("Project not found: " + projectId);
        }

        // 保存的历史记录，用于回滚
        ProjectHistory savedHistory = null;

        try {
            List<ProjectSupplier> supplierList = projectSupplierRepository.findByProjectId(projectId);

            // 获取项目供应商相关的所有草稿
            List<ProjectDraft> drafts = projectDraftRepository.getByProjectIdAndType(projectId, HistoryType.SUPPLIER);
            if (drafts.isEmpty()) {
                log.info("No supplier drafts found for project: " + projectId);
                return;
            }

            ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(
                    HistoryType.SUPPLIER,
                    supplierList, drafts);
            ProjectHistory history = new ProjectHistory(projectId);
            history.setDetailList(Lists.list(historyDetail));
            projectHistoryService.saveHistory(history);
            savedHistory = history;

            // 发起工作流
            // TODO: 集成具体的工作流引擎，启动项目供应商变更审批流程
            workflowService.startWorkflow(history);

        } catch (WorkflowStartException e) {
            // 工作流启动失败，手动回滚已保存的历史记录
            projectHistoryService.rollbackHistory(savedHistory);
            throw new ServerException("Failed to start workflow for project supplier: " + projectId, e);
        } catch (Exception e) {
            throw new ServerException("Failed to commit project supplier: " + projectId, e);
        }

        // 清理草稿（可选，根据业务需求决定是否在此处清理）
        // for (ProjectDraft draft : drafts) {
        // projectDraftRepository.deleteByTargetId(draft.getTargetId());
        // }
    }

    /**
     * 根据供应商ID查询关联的项目列表
     *
     * @param supplierId 供应商ID
     * @return 项目简单信息列表
     */
    public List<ProjectSimpleVO> loadProjectSelectList(String supplierId) {
        // 查询供应商关联的项目关系
        List<ProjectSupplier> projectSuppliers = projectSupplierRepository.findBySupplierId(supplierId);

        if (projectSuppliers.isEmpty()) {
            return new ArrayList<>();
        }

        // 提取项目ID列表
        List<String> projectIds = projectSuppliers.stream()
                .map(ProjectSupplier::getProjectId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询项目信息
        List<Project> projects = projectRepository.loadList(projectIds);
        List<ProjectSetting> projectSettings = projectSettingRepository.loadList(projectIds);

        // 转换为VO
        return projects.stream()
                .map(project -> {
                    ProjectSimpleVO vo = ObjectConverter.convert(project, ProjectSimpleVO.class);
                    Option<ProjectSetting> settingOption = Sequences.sequence(projectSettings)
                            .find(s -> project.getBid().equals(s.getProjectId()));
                    if (settingOption.isDefined()) {
                        vo.setSetting(ObjectConverter.convert(settingOption.get(), ProjectSettingVO.class));
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    public List<SupplierSelectVO> getProjectSuppliersSelectList(String projectId) {
        // 分页查询项目供应商关系
        List<ProjectSupplier> projectSupplierList = projectSupplierRepository.findByProjectId(projectId);
        if (projectSupplierList.isEmpty()) {
            return new ArrayList<>();
        }

        List<SupplierPageVO> supplier = supplierService.listSupplier(Sequences.sequence(projectSupplierList)
                .map(ProjectSupplier::getSupplierId).toList());
        // 转换为VO
        List<SupplierSelectVO> voList = Sequences.sequence(supplier).map(s -> {
            SupplierSelectVO vo = ObjectConverter.convert(s, SupplierSelectVO.class);
            return vo;
        }).toList();
        return voList;
    }
}
