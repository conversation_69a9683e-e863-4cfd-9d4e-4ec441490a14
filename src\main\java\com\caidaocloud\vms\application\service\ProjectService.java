package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.factory.ProjectFactory;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/6/3
 */
@Service
public class ProjectService {
	@Autowired
	private ProjectRepository projectRepository;

	@Autowired
	private ProjectHistoryFactory projectHistoryFactory;
	@Autowired
	private ProjectHistoryService projectHistoryService;

	@Autowired
	private ProjectSettingRepository projectSettingRepository;

	@Autowired
	private ProjectDraftRepository projectDraftRepository;

	@Autowired
	private WorkflowService workflowService;

	/**
	 * 传入项目编码（必填）、项目名称（必填）、开始日期（时间戳），结束日期，预算总额，计划人员数量，项目负责人，所属公司，备注
	 * 保存项目基本信息，并初始化项目设置
	 *
	 * @return
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.CREATE)
	public String save(ProjectDto projectDto) {
		// 创建项目基本信息
		Project project = ProjectFactory.create(projectDto);
		// 保存项目
		projectRepository.saveOrUpdate(project);
		projectSettingRepository.init(project.getProjectSetting(), project.getBid());
		return project.getBid();
	}

	/**
	 * 编辑项目信息
	 * 
	 * @param projectDto 项目信息
	 */
	// @PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.UPDATE)
	public void edit(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		// TODO: 2025/6/5 校验状态，不能修改数据
		// TODO: 2025/10/14 已用预算、实际人员

		// 更新项目信息
		project.updateBasicInfo(projectDto);
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	/**
	 * 分页查询项目列表
	 * 
	 * @param queryDTO 查询条件
	 * @return 项目列表
	 */
	public PageResult<ProjectVO> projectPage(ProjectQueryDTO queryDTO) {
		// 执行分页查询
		PageResult<Project> projectPage = projectRepository.findByPage(queryDTO.getProjectName(), queryDTO);

		// 转换为VO列表
		List<ProjectVO> voList = projectPage.getItems().stream()
				.map(project -> {
					ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);
					// 设置状态枚举
					if (project.getStatus() != null) {
						vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
					}
					return vo;
				})
				.collect(Collectors.toList());

		return new PageResult<>(voList, projectPage.getPageNo(), projectPage.getPageSize(), projectPage.getTotal());
	}

	/**
	 * 根据ID加载项目基础信息
	 * 
	 * @param projectId 项目ID
	 * @return 项目详细信息
	 */
	public ProjectVO loadProject(String projectId) {
		// 根据ID查询项目
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 转换为VO
		ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);

		// 设置状态枚举
		if (project.getStatus() != null) {
			vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
		}

		return vo;
	}

	/**
	 * 删除项目
	 * 
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.DELETE)
	public void delete(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}

		// 删除项目
		projectRepository.delete(project);
	}

	/**
	 * 提交项目变更
	 * 根据draft生成change，保存historyDetail，发起工作流
	 * 无分布式事务，手动回滚
	 *
	 * @param projectId 项目ID
	 */
	public void commitProject(String projectId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 获取项目相关的所有草稿
		Optional<ProjectDraft> optional = projectDraftRepository.getByTargetId(project.getBid());
		if (!optional.isPresent()) {
			throw new ServerException("No drafts found for project: " + projectId);
		}

		ProjectHistory projectHistory = null;
		try {
			ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(project, optional.get());
			projectHistory = new ProjectHistory(projectId, historyDetail);
			projectHistoryService.saveHistory(projectHistory);

			workflowService.startWorkflow(projectHistory);

		} catch (WorkflowStartException e) {
			projectHistoryService.rollbackHistory(projectHistory);
			throw new ServerException("Failed to start workflow for project: " + projectId, e);
		} catch (Exception e) {
			throw new ServerException("Failed to commit project: " + projectId, e);
		}

		// 清理草稿（可选，根据业务需求决定是否在此处清理）
		// projectDraftRepository.deleteByProjectId(projectId);
	}

}
