package com.caidaocloud.vms.application.dto;

import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "岗位供应商关系信息")
public class PositionSupplierDto {

    @ApiModelProperty(value = "关系ID")
    private String bid;

    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;

    @ApiModelProperty(value = "岗位ID", required = true)
    private String positionId;

    @ApiModelProperty(value = "供应商ID", required = true)
    private String supplierId;

    @ApiModelProperty("供应商联系人")
    private List<String> supplierContact;

    @ApiModelProperty(value = "报价模式")
    private QuotationMode quotationMode;

    @ApiModelProperty(value = "报价金额")
    private BigDecimal quotationValue;
}
