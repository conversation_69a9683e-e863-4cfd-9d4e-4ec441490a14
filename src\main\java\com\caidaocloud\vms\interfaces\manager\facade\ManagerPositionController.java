package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
import com.caidaocloud.vms.application.service.PositionManagementService;
import com.caidaocloud.vms.application.vo.PositionManagementPageVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用工方岗位管理控制器
 * 
 * <AUTHOR>
 * @date 2025/10/23
 */
@RestController
@RequestMapping("/api/vms/v1/supplier/position")
@Api(tags = "用工方岗位管理", description = "用工方查询所有项目岗位的接口")
@Validated
public class ManagerPositionController {

    @Autowired
    private PositionManagementService positionManagementService;

    /**
     * 用工方分页查询所有项目岗位
     * 
     * @param queryDTO 查询条件
     * @return 岗位分页列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询所有项目岗位", notes = "用工方可以查询所有项目下的岗位信息，支持多种条件筛选")
    public Result<PageResult<PositionManagementPageVO>> getPositionPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody PositionManagementQueryDTO queryDTO) {
        
        queryDTO.setSupplierId(null);
        
        PageResult<PositionManagementPageVO> result = positionManagementService.loadPositionPage(queryDTO);
        return Result.ok(result);
    }

}
