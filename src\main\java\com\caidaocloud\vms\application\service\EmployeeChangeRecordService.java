package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.EmployeeChangeRecordQueryDTO;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.PostService;
import com.caidaocloud.vms.application.vo.EmployeeChangeRecordPageVO;
import com.caidaocloud.vms.domain.employee.entity.Employee;
import com.caidaocloud.vms.domain.employee.entity.EmployeeChangeRecord;
import com.caidaocloud.vms.domain.employee.enums.EmployeeChangeType;
import com.caidaocloud.vms.domain.employee.repository.EmployeeChangeRecordRepository;
import com.caidaocloud.vms.domain.employee.repository.EmployeeRepository;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.caidaocloud.vms.domain.supplier.entity.Supplier;
import com.caidaocloud.vms.domain.supplier.repository.SupplierRepository;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工入离项记录服务
 * 
 * <AUTHOR> Zhou
 * @date 2025/10/29
 */
@Service
public class EmployeeChangeRecordService {

    @Autowired
    private EmployeeChangeRecordRepository employeeChangeRecordRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private PostService postService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private EmployeeRepository employeeRepository;

    /**
     * 分页查询员工入离项记录
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public PageResult<EmployeeChangeRecordPageVO> findByPage(EmployeeChangeRecordQueryDTO queryDTO) {
        // 分页查询记录
        PageResult<EmployeeChangeRecord> pageResult = employeeChangeRecordRepository.findByPage(queryDTO);

        // 组装展示数据
        return assemblePageVO(pageResult);
    }

    /**
     * 保存或更新员工入离项记录
     * 
     * @param record 记录实体
     */
    public void saveOrUpdate(EmployeeChangeRecord record) {
        employeeChangeRecordRepository.saveOrUpdate(record);
    }

    /**
     * 根据ID获取员工入离项记录
     * 
     * @param recordId 记录ID
     * @return 记录实体
     */
    public EmployeeChangeRecord getById(String recordId) {
        return employeeChangeRecordRepository.getById(recordId);
    }

    /**
     * 删除员工入离项记录
     * 
     * @param recordId 记录ID
     */
    public void deleteById(String recordId) {
        employeeChangeRecordRepository.deleteById(recordId);
    }

    /**
     * 组装分页展示数据
     * 
     * @param pageResult 分页查询结果
     * @return 组装后的展示数据
     */
    private PageResult<EmployeeChangeRecordPageVO> assemblePageVO(PageResult<EmployeeChangeRecord> pageResult) {
        List<EmployeeChangeRecord> records = pageResult.getItems();
        if (records.isEmpty()) {
            return new PageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(),
                    pageResult.getTotal());
        }

        // 收集需要查询的ID列表
        List<String> projectIds = records.stream()
                .map(EmployeeChangeRecord::getProjectId)
                .distinct()
                .collect(Collectors.toList());

        List<String> supplierIds = records.stream()
                .map(EmployeeChangeRecord::getSupplierId)
                .distinct()
                .collect(Collectors.toList());

        List<String> positionIds = records.stream()
                .map(EmployeeChangeRecord::getPositionId)
                .distinct()
                .collect(Collectors.toList());

        List<String> companyIds = records.stream()
                .map(EmployeeChangeRecord::getCompanyId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询关联数据
        List<Project> projects = projectRepository.loadList(projectIds);

        List<Supplier> suppliers = supplierRepository.list(supplierIds);
        List<PostInfoDto> postList = postService.loadPostList(positionIds);
        List<CompanyInfoDto> companyList = companyService.loadCompanyList(companyIds);

        // 组装VO
        List<EmployeeChangeRecordPageVO> voList = records.stream().map(record -> {
            EmployeeChangeRecordPageVO vo = ObjectConverter.convert(record, EmployeeChangeRecordPageVO.class);
            // 组装项目信息
            Option<Project> projectOpt = Sequences.sequence(projects)
                    .find(project -> record.getProjectId().equals(project.getBid()));
            if (projectOpt.isDefined()) {
                vo.setProjectName(projectOpt.get().getProjectName());
            }

            // 组装供应商信息
            Option<Supplier> supplierOpt = Sequences.sequence(suppliers)
                    .find(supplier -> record.getSupplierId().equals(supplier.getBid()));
            if (supplierOpt.isDefined()) {
                vo.setSupplierName(supplierOpt.get().getSupplierName());
            }

            // 组装岗位信息
            Option<PostInfoDto> postOpt = Sequences.sequence(postList)
                    .find(post -> record.getPositionId().equals(post.getBid()));
            if (postOpt.isDefined()) {
                vo.setPositionName(postOpt.get().getName());
            }

            // 组装公司信息
            Option<CompanyInfoDto> companyOpt = Sequences.sequence(companyList)
                    .find(company -> record.getCompanyId().equals(company.getBid()));
            if (companyOpt.isDefined()) {
                vo.setCompanyName(companyOpt.get().getCompanyName());
            }

            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }
}
