# 工作流注册方法重构文档

## 重构概述

本次重构对 `WorkflowService` 类的 `register` 方法进行了重构，使用枚举定义 function 和 callback 配置，并确保在注册 function 后注册 callback。

## 重构内容

### 1. 创建工作流配置枚举 `WorkflowConfig`

新增了 `WorkflowConfig` 枚举类，用于统一管理工作流的配置信息：

```java
public enum WorkflowConfig {
    PROJECT_MANAGEMENT("项目管理", "VMS_PROJECT", "/api/vms/v1/project/detail", callbacks),
    PROJECT_POSITION("项目管理-岗位", "VMS_POSITION", "/api/vms/v1/project/detail", callbacks);
}
```

**枚举特性：**
- 包含工作流名称、代码、详情页面路径
- 内置 `CallbackConfig` 内部类定义回调配置
- 每个工作流配置包含 APPROVE、REJECT、CANCEL 三种回调

### 2. 重构 `register` 方法

#### 新的主要注册方法
```java
public void register(WorkflowConfig workflowConfig) {
    // 1. 先注册function
    registerFunction(workflowConfig);
    
    // 2. 再注册callback
    registerCallback(workflowConfig);
}
```

#### 兼容性方法
```java
@Deprecated
public void register(String name, String code) {
    // 尝试从枚举中查找配置，如果找不到则使用旧方式
}
```

### 3. 拆分注册逻辑

将原来的注册逻辑拆分为两个私有方法：

- `registerFunction(WorkflowConfig)`: 注册工作流 function
- `registerCallback(WorkflowConfig)`: 注册工作流 callback

### 4. 更新调用方式

在 `ProjectSettingService.initWorkflow()` 中更新了调用方式：

```java
// 旧方式（已废弃）
workflowService.register("项目管理", ProjectConstant.PROJET_WORK_FLOW_CODE);

// 新方式
workflowService.register(WorkflowConfig.PROJECT_MANAGEMENT);
workflowService.register(WorkflowConfig.PROJECT_POSITION);
```

## 重构优势

### 1. 配置集中化
- 所有工作流配置统一在枚举中管理
- 避免了硬编码的字符串和配置分散的问题

### 2. 类型安全
- 使用枚举提供编译时类型检查
- 减少配置错误的可能性

### 3. 可维护性
- 新增工作流只需在枚举中添加配置
- 配置变更只需修改枚举定义

### 4. 执行顺序保证
- 明确保证先注册 function，再注册 callback
- 避免了注册顺序导致的问题

### 5. 向后兼容
- 保留了旧的注册方法（标记为 @Deprecated）
- 现有代码可以继续工作

## 回调配置详情

每个工作流配置包含以下回调：

| 回调类型 | 描述 | 代码格式 | 路径示例 |
|---------|------|----------|----------|
| APPROVE | 通过 | WF_CALLBACK_APPROVE | /api/vms/v1/project/workflow/approve |
| REJECT  | 拒绝 | WF_CALLBACK_REJECT  | /api/vms/v1/project/workflow/reject |
| CANCEL  | 取消 | WF_CALLBACK_CANCEL  | /api/vms/v1/project/workflow/cancel |

## 使用示例

### 注册项目管理工作流
```java
workflowService.register(WorkflowConfig.PROJECT_MANAGEMENT);
```

### 注册项目岗位工作流
```java
workflowService.register(WorkflowConfig.PROJECT_POSITION);
```

### 添加新的工作流配置
```java
// 在 WorkflowConfig 枚举中添加
NEW_WORKFLOW(
    "新工作流", 
    "NEW_WORKFLOW_CODE",
    "/api/vms/v1/new/detail",
    new CallbackConfig[]{
        new CallbackConfig("APPROVE", "通过", "/api/vms/v1/new/workflow/approve"),
        new CallbackConfig("REJECT", "拒绝", "/api/vms/v1/new/workflow/reject"),
        new CallbackConfig("CANCEL", "取消", "/api/vms/v1/new/workflow/cancel")
    }
);
```

## 注意事项

1. **旧方法废弃**: 旧的 `register(String, String)` 方法已标记为 @Deprecated，建议迁移到新方法
2. **枚举扩展**: 新增工作流时需要在 `WorkflowConfig` 枚举中添加配置
3. **回调路径**: 确保回调路径对应的 Controller 方法已实现
4. **日志记录**: 新方法增加了详细的日志记录，便于调试和监控

## 测试建议

1. 验证新的注册方法能正确注册 function 和 callback
2. 验证旧方法的兼容性
3. 验证枚举配置的正确性
4. 验证回调路径的可访问性

## 总结

本次重构通过引入枚举配置，实现了工作流注册的标准化和规范化，提高了代码的可维护性和可扩展性，同时保证了向后兼容性。
