package com.caidaocloud.vms.application.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.interfaces.manager.facade.ProjectSupplierController;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2025/11/3
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TmTest {

	@Autowired
	private ProjectSupplierController projectSupplierController;

	@Before
	public void bf(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void list(){
		ProjectSupplierQueryDTO dto = new ProjectSupplierQueryDTO();
		dto.setProjectId("2310006975248384");
		projectSupplierController.getProjectSuppliers(dto);
	}
}
