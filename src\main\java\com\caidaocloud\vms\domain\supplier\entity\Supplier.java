package com.caidaocloud.vms.domain.supplier.entity;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.SupplierDto;
import com.caidaocloud.vms.application.dto.SupplierTaxInfoDto;
import com.caidaocloud.vms.domain.supplier.enums.SupplierStatus;
import lombok.Data;

@Data
public class Supplier extends DataSimple {

    private String supplierCode;

    private String supplierName;

    private DictSimple country;

    private Address province;

    private String postalCode;

    private String phone;

    private String address;

    private String remarks;

    private DictSimple staffSize;

    private DictSimple revenueScale;

    private List<String> industry;

    private String rating;

    private Long startDate;

    private Long endDate;

    private EnumSimple status;

    private SupplierTaxInfo taxInfo;

    public static String identifier = "entity.vms.Supplier";

    private Supplier() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
    }

    public Supplier(String supplierCode, String supplierName) {
        this();
        this.supplierCode = Objects.requireNonNull(supplierCode, "Supplier code cannot be null");
        this.supplierName = Objects.requireNonNull(supplierName, "Supplier name cannot be null");
        status = SupplierStatus.COOPERATING.toEnumSimple();
        // 初始化税务信息
        SupplierTaxInfo taxInfo = new SupplierTaxInfo();
        setTaxInfo(taxInfo);
    }


    public void updateBasicInfo(SupplierDto supplierDto) {
        setSupplierName(supplierDto.getSupplierName());
        setCountry(supplierDto.getCountry());
        setProvince(supplierDto.getProvince());
        setPostalCode(supplierDto.getPostalCode());
        setPhone(supplierDto.getPhone());
        setAddress(supplierDto.getAddress());
        setRemarks(supplierDto.getRemarks());
        setStaffSize(supplierDto.getStaffSize());
        setRevenueScale(supplierDto.getRevenueScale());
        setIndustry(supplierDto.getIndustry());
        setRating(supplierDto.getRating());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void updateTaxInfo(SupplierTaxInfoDto taxInfoDto) {
        // 更新税务信息
        taxInfo.setCompanyName(taxInfoDto.getCompanyName());
        taxInfo.setBusinessLicense(taxInfoDto.getBusinessLicense());
        taxInfo.setTaxId(taxInfoDto.getTaxId());
        taxInfo.setPhone(taxInfoDto.getPhone());
        taxInfo.setAddress(taxInfoDto.getAddress());
        taxInfo.setBankName(taxInfoDto.getBankName());
        taxInfo.setBankAccount(taxInfoDto.getBankAccount());
        taxInfo.setUpdateTime(System.currentTimeMillis());
      taxInfo.  setUpdateTime(System.currentTimeMillis());
      taxInfo.  setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void updateContract(SupplierContract contract) {
        startDate = Optional.ofNullable(startDate).map(s -> Math.min(s, contract.getEffectiveDate()))
                .orElse(contract.getEffectiveDate());
        endDate = Optional.ofNullable(endDate).map(s -> Math.max(s, contract.getExpiryDate()))
                .orElse(contract.getExpiryDate());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public void terminated() {
      status=(SupplierStatus.TERMINATED.toEnumSimple());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }
}