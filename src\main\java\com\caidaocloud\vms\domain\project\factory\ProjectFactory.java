package com.caidaocloud.vms.domain.project.factory;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.domain.project.entity.Project;
import org.apache.commons.collections.Factory;

import org.springframework.beans.BeanUtils;

public class ProjectFactory{
    public static Project create(ProjectDto projectDto) {
        Project project = new Project(projectDto.getProjectCode(), projectDto.getProjectName());
        if (projectDto.getBid() != null) {
            // 使用HistoryAspect中生成的bid
            project.setBid(projectDto.getBid());
        }
        // 设置项目其他属性
        BeanUtils.copyProperties(projectDto, project);
        project.setProjectManager(new EmpSimple());
        project.getProjectManager().setEmpId(projectDto.getProjectManager());
        return project;
    }
}
