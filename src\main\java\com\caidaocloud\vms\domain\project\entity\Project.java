package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.domain.project.dto.FormatResult;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)

public class Project extends DetailedHistoryFormat<Project> {
    private String projectCode;

    private String projectName;

    private Long startDate;

    private Long endDate;

    private BigDecimal totalBudget;

    private BigDecimal usedBudget;

    private Integer plannedHeadcount;

    private Integer actualHeadcount;

    private EmpSimple projectManager;

    private String company;

    private String remark;

    private EnumSimple status;

    private List<ProjectSupplier> supplierList = new ArrayList<>();

    private List<ProjectContact> contactList = new ArrayList<>();

    private List<ProjectPosition> positionList = new ArrayList<>();

    private ProjectSetting projectSetting;

    private String version;

    public static String identifier = "entity.vms.Project";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public Project(String projectCode, String projectName) {
        this();
        this.projectCode = Objects.requireNonNull(projectCode, "Project code cannot be null");
        this.projectName = Objects.requireNonNull(projectName, "Project name cannot be null");
        status = ProjectStatus.NEW.toEnumSimple();
        projectSetting = new ProjectSetting();
        version = SnowUtil.nextId();
    }

    public void updateBasicInfo(ProjectDto projectDto) {
        this.projectName = projectDto.getProjectName();
        this.startDate = projectDto.getStartDate();
        this.endDate = projectDto.getEndDate();
        this.totalBudget = projectDto.getTotalBudget();
        this.plannedHeadcount = projectDto.getPlannedHeadcount();
        this.projectManager = new EmpSimple();
        projectManager.setEmpId(projectDto.getProjectManager());
        this.company = projectDto.getCompany();
        this.remark = projectDto.getRemark();
        if (startDate > endDate) {
            throw new ServerException("开始时间必须早于结束时间");
        }

        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public ProjectPosition createPosition(String position, String company, String organization) {
        return new ProjectPosition(getBid(), position, company, organization);
    }
    //
    // public Pair<String, List<ProjectChange>> formatAll(Option<ProjectDraft>
    // projectDraft,List<ProjectDraft> draftList) {
    //
    // if (projectDraft.isDefined()) {
    // List<ProjectChange> projectChangeList = format(projectDraft.get());
    // Project snapshot = FastjsonUtil.toObject(projectDraft.get().getSnapshot(),
    // Project.class);
    // }
    // Pair<?, List<ProjectChange>> pair = format(projectSetting, draftList,
    // ProjectSetting.class);
    // snapshot.setProjectSetting((ProjectSetting) pair.first());
    // projectChangeList.addAll(pair.second());
    //
    // for (ProjectSupplier data : supplierList) {
    // Pair<?, List<ProjectChange>> p = format(data, draftList,
    // ProjectSupplier.class);
    // snapshot.supplierList.add((ProjectSupplier) p.first());
    // projectChangeList.addAll(p.second());
    // }
    // for (ProjectContact data : contactList) {
    // Pair<?, List<ProjectChange>> p = format(data, draftList,
    // ProjectContact.class);
    // snapshot.contactList.add((ProjectContact) p.first());
    // projectChangeList.addAll(p.second());
    // }
    // // TODO: 2025/10/16 岗位单独审批
    // for (ProjectPosition data : positionList) {
    // Pair<?, List<ProjectChange>> p = format(data, draftList,
    // ProjectPosition.class);
    // snapshot.positionList.add((ProjectPosition) p.first());
    // projectChangeList.addAll(p.second());
    // }
    //
    // return Pair.pair(FastjsonUtil.toJson(snapshot), projectChangeList);
    // }

    public List<ProjectHistoryDetail> formatAll(Option<ProjectDraft> projectDraft, List<ProjectDraft> draftList,
            boolean postWorkflowEnabled) {
        List<ProjectHistoryDetail> list = new ArrayList<>();
        if (projectDraft.isDefined()) {
            List<ProjectChange> projectChangeList = format(projectDraft.get());
            list.add(new ProjectHistoryDetail(HistoryType.BASIC_INFO, projectDraft.get().getSnapshot(),
                    projectChangeList, Lists.list(projectDraft.get().getBid())));
        }

        // TODO: 2025/10/16
        Option<ProjectDraft> settingDraft = Sequences.sequence(draftList)
                .find(d -> d.getType().getValue().equals(HistoryType.SETTING.getValue()));
        if (settingDraft.isDefined()) {
            List<ProjectChange> settingChange = projectSetting.format(settingDraft.get());
            list.add(new ProjectHistoryDetail(HistoryType.SETTING, settingDraft.get()
                    .getSnapshot(), settingChange, Lists.list(settingDraft.get().getBid())));
        }

        List<ProjectDraft> supplierDraftList = Sequences.sequence(draftList)
                .filter(d -> d.getType().getValue().equals(HistoryType.SUPPLIER.getValue()))
                .toList();
        if (CollectionUtils.isNotEmpty(supplierDraftList)) {
            List<ProjectSupplier> sl = new ArrayList<>();
            List<ProjectChange> scl = new ArrayList<>();
            List<String> sd = new ArrayList<>();
            for (ProjectSupplier supplier : supplierList) {
                FormatResult<ProjectSupplier> result = format(supplier, supplierDraftList, ProjectSupplier.class);
                sl.add(result.getEntity());
                scl.addAll(result.getChanges());
                sd.addAll(result.getDraftIdList());
            }
            list.add(new ProjectHistoryDetail(HistoryType.SUPPLIER, FastjsonUtil.toJson(sl), scl, sd));
        }

        List<ProjectDraft> contactDraft = Sequences.sequence(draftList)
                .filter(d -> d.getType().getValue().equals(HistoryType.CONTACT.getValue()))
                .toList();
        if (CollectionUtils.isNotEmpty(contactDraft)) {
            List<ProjectContact> cl = new ArrayList<>();
            List<ProjectChange> ccl = new ArrayList<>();
            List<String> cd = new ArrayList<>();
            for (ProjectContact data : contactList) {
                FormatResult<ProjectContact> result = format(data, draftList, ProjectContact.class);
                cl.add(result.getEntity());
                ccl.addAll(result.getChanges());
                cd.addAll(result.getDraftIdList());
            }
            list.add(new ProjectHistoryDetail(HistoryType.CONTACT, FastjsonUtil.toJson(cl), ccl, cd));
        }

        // TODO: 2025/10/16 岗位单独审批
        if (!postWorkflowEnabled) {
            List<ProjectDraft> positionDraft = Sequences.sequence(draftList)
                    .filter(d -> d.getType().getValue().equals(HistoryType.POSITION.getValue()))
                    .toList();
            if (CollectionUtils.isNotEmpty(positionDraft)) {
                List<ProjectPosition> pl = new ArrayList<>();
                List<ProjectChange> pcl = new ArrayList<>();
                List<String> pd = new ArrayList<>();
                for (ProjectPosition data : positionList) {
                    FormatResult<ProjectPosition> result = format(data, draftList, ProjectPosition.class);
                    pl.add(result.getEntity());
                    pcl.addAll(result.getChanges());
                    pd.addAll(result.getDraftIdList());
                }
                list.add(new ProjectHistoryDetail(HistoryType.POSITION, FastjsonUtil.toJson(pl), pcl, pd));
            }
        }

        return list;
    }

    public void approve() {
        update();
        status = ProjectStatus.APPROVED.toEnumSimple();
    }

    public void reject() {
        update();
        status = ProjectStatus.REJECT.toEnumSimple();
    }

    public void start() {
        update();
        status = ProjectStatus.IN_PROGRESS.toEnumSimple();
    }


    public void end() {
        update();
        status = ProjectStatus.CLOSED.toEnumSimple();
    }

    public void checkUpdate() {
        ProjectStatus status = ProjectStatus.fromCode(Integer.parseInt(this.status.getValue()));
        if (status != ProjectStatus.NEW && status != ProjectStatus.REJECT && status != ProjectStatus.APPROVED) {
            throw new ServerException("项目不可编辑");
        }
    }
}
