package com.caidaocloud.vms.domain.project.repository;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.domain.project.entity.Project;

public interface ProjectRepository {
    String saveOrUpdate(Project project);
    
    Project getById(String projectId);
    
    PageResult<Project> findByPage(String projectName, ProjectQueryDTO queryDTO);
    
    void delete(Project project);

	List<Project> loadList(List<String> projectIds);
}