package com.caidaocloud.vms.application.dto;

import com.caidaocloud.vms.domain.supplier.enums.ContactStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierContactDto {
    private String bid;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "联系人姓名")
    private String contact;

    @ApiModelProperty(value = "联系人职务")
    private String position;

    @ApiModelProperty(value = "电子邮件")
    private String email;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "备注")
    private String remarks;

}