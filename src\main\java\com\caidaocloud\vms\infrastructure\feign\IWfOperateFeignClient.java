package com.caidaocloud.vms.infrastructure.feign;

import java.util.List;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiParam;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "caidaocloud-workflow-service-v2",
        fallback = WfOperateFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "wfOperateFeignClient"
)
public interface IWfOperateFeignClient {


    /**
     * 检查流程是否已启用
     *
     * @param funCode 流程code
     * @return 布尔
     */
    @GetMapping("/api/workflow/v2/config/def/checkEnabled")
    Result<Boolean> checkDefEnabled(@RequestParam("funCode") String funCode);
}
