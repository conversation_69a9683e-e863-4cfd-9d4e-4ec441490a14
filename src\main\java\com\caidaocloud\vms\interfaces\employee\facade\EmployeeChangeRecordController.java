package com.caidaocloud.vms.interfaces.employee.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.EmployeeChangeRecordQueryDTO;
import com.caidaocloud.vms.application.dto.OnboardingSaveDTO;
import com.caidaocloud.vms.application.service.EmployeeChangeRecordService;
import com.caidaocloud.vms.application.service.OnboardingService;
import com.caidaocloud.vms.application.vo.EmployeeChangeRecordPageVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 员工入离项管理控制器
 * 
 * <AUTHOR>
 * @date 2025/10/29
 */
@RestController
@RequestMapping("/api/vms/v1/manager/employee-change-record")
@Api(tags = "员工入离项管理", description = "员工入离项记录的查询接口")
public class EmployeeChangeRecordController {

    @Autowired
    private EmployeeChangeRecordService employeeChangeRecordService;

    @Autowired
    private OnboardingService onboardingService;

    /**
     * 分页查询员工入离项记录
     * 
     * @param queryDTO 查询条件
     * @return 入离项记录分页列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询员工入离项记录")
    public Result<PageResult<EmployeeChangeRecordPageVO>> getChangeRecordPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody EmployeeChangeRecordQueryDTO queryDTO) {

        PageResult<EmployeeChangeRecordPageVO> result = employeeChangeRecordService.findByPage(queryDTO);
        return Result.ok(result);
    }

    /**
     * 保存预入职信息
     *
     * @param onboardingDTO 预入职信息
     * @return 员工ID
     */
    @PostMapping("/onboarding/save")
    @ApiOperation(value = "保存预入职信息", notes = "创建员工信息并生成预入职入离项记录")
    public Result<String> saveOnboarding(
            @ApiParam(value = "预入职信息", required = true) @RequestBody @Validated OnboardingSaveDTO onboardingDTO) {

        String empId = onboardingService.saveOnboarding(onboardingDTO);
        return Result.ok(empId);
    }
}
