package com.caidaocloud.vms.application.service.emp;

import java.util.List;

import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.domain.base.repository.PostRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/10/15
 */
@Service
public class PostService {
	@Autowired
	private PostRepository postRepository;

	public List<PostInfoDto> loadPostList(List<String> postIds){
		return postRepository.loadPostList(postIds);
	}
}
