package com.caidaocloud.vms.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.application.dto.ProjectSettingDto;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.WorkflowConfig;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

import javax.annotation.PostConstruct;
import javax.swing.Spring;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 项目设置服务类
 * 
 * <AUTHOR> Zhou
 * @date 2025/9/27
 */
@Service
@Slf4j
public class ProjectSettingService {

    @Autowired
    private ProjectSettingRepository projectSettingRepository;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private ProjectDraftRepository projectDraftRepository;

    @Autowired
    private ProjectHistoryFactory projectHistoryFactory;

    @Autowired
    private ProjectHistoryService projectHistoryService;

    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private ProjectPositionService projectPositionService;

    @PostConstruct
    public void initWorkflow() {
        try {
            // 使用枚举方式注册工作流，会自动注册function和callback
            workflowService.register(WorkflowConfig.PROJECT_MANAGEMENT);
            workflowService.register(WorkflowConfig.PROJECT_POSITION);
        } catch (Exception e) {
            log.error("registerFunction Exception,{}", e);
        }
    }

    /**
     * 保存或更新项目设置
     *
     * @param settingDto 项目设置信息
     * @return 设置ID
     */
    @PaasTransactional
    @HistoryRecord(dtoTypes = {ProjectSettingDto.class }, historyType = HistoryType.SETTING, operationType = OperationType.UPDATE)
    public void edit(ProjectSettingDto settingDto) {
        ProjectSetting projectSetting;

        // 更新现有设置
        Optional<ProjectSetting> existingSetting = projectSettingRepository.getByProjectId(settingDto.getProjectId());
        if (!existingSetting.isPresent()) {
            throw new ServerException("Project setting not found: " + settingDto.getBid());
        }
        projectSetting = existingSetting.get();
        // 更新字段
        BeanUtils.copyProperties(settingDto, projectSetting, "bid", "createTime", "createBy");
        projectSetting.update(); // 更新时间戳

        projectSettingRepository.saveOrUpdate(projectSetting);

    }

    /**
     * 根据项目ID获取项目设置
     * 
     * @param projectId 项目ID
     * @return 项目设置信息
     */
    public ProjectSettingVO getByProjectId(String projectId) {
        Optional<ProjectSetting> settingOpt = projectSettingRepository.getByProjectId(projectId);
        if (!settingOpt.isPresent()) {
            throw new ServerException("Project setting not found for project: " + projectId);
        }

        return ObjectConverter.convert(settingOpt.get(), ProjectSettingVO.class);
    }

    /**
     * 提交项目设置变更
     * 根据draft生成change，保存historyDetail，发起工作流
     * 无分布式事务，手动回滚
     *
     * @param projectId 项目ID
     */
    public void commitProjectSetting(ProjectSettingDto dto) {
        SpringUtil.getBean(ProjectSettingService.class).edit(dto);
        doCommit(dto.getProjectId());
        //
        //
        // // 根据草稿生成历史详情记录
        // for (ProjectDraft draft : drafts) {
        // // 获取原始设置数据
        // Optional<ProjectSetting> originalSettingOpt =
        // projectSettingRepository.getByProjectId(draft.getTargetId());
        // if (originalSettingOpt.isPresent()) {
        // ProjectHistoryDetail detail = projectHistoryFactory
        // .generateHistoryDetailFromDraft(originalSettingOpt.get(), draft);
        // projectHistoryService.saveDetail(detail);
        // }
        // }

        // 发起工作流

        // 清理草稿（可选，根据业务需求决定是否在此处清理）
        // for (ProjectDraft draft : drafts) {
        // projectDraftRepository.deleteByTargetId(draft.getTargetId());
        // }
    }

    private void doCommit(String projectId) {
        // 获取项目信息
        Project project = projectRepository.getById(projectId);
        if (project == null) {
            throw new ServerException("Project not found: " + projectId);
        }

        // 保存的历史记录，用于回滚
        ProjectHistory savedHistory = null;

        try {
            ProjectSetting projectSetting = projectSettingRepository.getByProjectId(projectId).get();
            List<ProjectDraft> draftList = projectDraftRepository.getByProjectId(projectId);
            if (CollectionUtils.isEmpty(draftList)) {
                log.info("No drafts found for project: " + projectId);
                return;
            }
            Option<ProjectDraft> draftOption = Sequences.sequence(draftList)
                    .find(d -> d.getTargetId().equals(projectId));

            // todo project加载所有entity
            List<ProjectHistoryDetail> projectHistoryDetailList = project.formatAll(draftOption, draftList,
                    projectSetting.getPositionApprovalFlow());
            ProjectHistory history = new ProjectHistory(projectId, projectHistoryDetailList);
            projectHistoryService.saveHistory(history);
            savedHistory = history;

            // 发起工作流
            // TODO: 集成具体的工作流引擎，启动项目设置变更审批流程
            workflowService.startWorkflow(history);

            if (projectSetting.getPositionApprovalFlow()) {
                projectPositionService.commitProjectPositionIndividually(projectId);
            }

        } catch (WorkflowStartException e) {
            // 工作流启动失败，手动回滚已保存的历史记录
            projectHistoryService.rollbackHistory(savedHistory);
            throw new ServerException("Failed to start workflow for project setting: " + projectId, e);
        } catch (Exception e) {
            throw new ServerException("Failed to commit project setting: " + projectId, e);
        }
    }
}
