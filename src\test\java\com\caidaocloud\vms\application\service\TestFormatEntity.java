package com.caidaocloud.vms.application.service;

import java.util.List;

import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;

/**
 *
 * <AUTHOR>
 * @date 2025/11/3
 */
public class TestFormatEntity extends DataSimpleHistoryFormat {
	@Override
	public String getEntityIdentifier() {
		return null;
	}

	@Override
	public List<ProjectChange> format(Object originData, ProjectDraft draft) {
		return null;
	}
}
