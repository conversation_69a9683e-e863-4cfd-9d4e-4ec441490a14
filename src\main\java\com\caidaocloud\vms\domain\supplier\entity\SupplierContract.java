package com.caidaocloud.vms.domain.supplier.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.SupplierContractDto;
import lombok.Data;

@Data
public class SupplierContract extends DataSimple {

    private String supplierId;

    private String contractName;

    private String contractNumber;

    private Long effectiveDate;

    private Long expiryDate;

    private Attachment attachment;

    public static String identifier = "entity.vms.SupplierContract";

    public SupplierContract() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
    }

    public SupplierContract(String supplierId, String contractNumber, Long effectiveDate, Long expiryDate) {
        this();
        this.supplierId = supplierId;
        this.contractNumber = contractNumber;
        this.effectiveDate = effectiveDate;
        this.expiryDate = expiryDate;
    }

    public void update(SupplierContractDto supplierContractDto) {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        this.contractName = supplierContractDto.getContractName();
        this.contractNumber = supplierContractDto.getContractNumber();
        this.effectiveDate = supplierContractDto.getEffectiveDate();
        this.expiryDate = supplierContractDto.getExpiryDate();
        this.attachment = supplierContractDto.getAttachment();
    }

    //
    // private static SuppliersRepository repository() {
    //     return SpringUtil.getBean(SuppliersRepository.class);
    // }
    //
    // public void delete() {
    //     repository().deleteContract(getBid());
    // }
    //
    // public String save() {
    //     setUpdateTime(System.currentTimeMillis());
    //     setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    //     setCreateTime(getUpdateTime());
    //     setCreateBy(getUpdateBy());
    //     return repository().saveContract(this);
    // }
}