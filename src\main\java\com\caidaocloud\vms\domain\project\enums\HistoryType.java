package com.caidaocloud.vms.domain.project.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

public enum HistoryType {
    BASIC_INFO("0", "基本信息", "BASIC_INFO"),
    SUPPLIER("1", "供应商", "SUPPLIER"),
    CONTACT("2", "联系人", "CONTACT"),
    POSITION("3", "岗位", "POSITION"),
    SETTING("4", "设置", "SETTING"),


    REQUIRE("10", "招聘要求", "REQUIREMENT"),

    NONE("-1","","");

    private final String value;
    private final String display;
    private final String code;

    HistoryType(String value, String display, String code) {
        this.value = value;
        this.display = display;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public String getCode() {
        return code;
    }

    // 根据value查找枚举
    public static HistoryType fromValue(String value) {
        for (HistoryType type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new ServerException("无效的页面类型值: " + value);
    }

    // 根据display查找枚举
    public static HistoryType fromDisplay(String display) {
        for (HistoryType type : values()) {
            if (type.display.equals(display)) {
                return type;
            }
        }
        throw new ServerException("无效的页面类型显示名称: " + display);
    }

    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(value);
        return enumSimple;
    }

    public boolean isArray() {
        return this == SUPPLIER || this == CONTACT || this == POSITION;
    }
}