package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectSupplierQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;

import java.util.List;
import java.util.Optional;

public interface ProjectSupplierRepository {
    String saveOrUpdate(ProjectSupplier projectSupplier);

    ProjectSupplier getById(String id);

    List<ProjectSupplier> findByProjectId(String projectId);

    PageResult<ProjectSupplier> findByPage(String projectId, String supplierName, ProjectSupplierQueryDTO queryDTO);

    void delete(ProjectSupplier supplier);

    boolean existsByProjectIdAndSupplierId(String projectId, String supplierId);

    /**
     * 根据ID获取项目供应商
     * 
     * @param supplierId 供应商ID
     * @return 项目供应商
     */
    Optional<ProjectSupplier> getSupplier(String supplierId);

    /**
     * 删除项目供应商
     *
     * @param supplier 项目供应商
     */
    void deleteSupplier(ProjectSupplier supplier);

    /**
     * 根据供应商ID查询关联的项目
     *
     * @param supplierId 供应商ID
     * @return 项目供应商关系列表
     */
    List<ProjectSupplier> findBySupplierId(String supplierId);
}