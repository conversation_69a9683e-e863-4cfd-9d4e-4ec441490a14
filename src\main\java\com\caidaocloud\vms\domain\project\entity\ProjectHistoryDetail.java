package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.annotation.ArrayEntityFormat;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@Slf4j
public class ProjectHistoryDetail extends BaseEntity {

    private String historyId;

    private String projectId;

    private EnumSimple type;

    private String approveBy;

    private Long approveTime;

    private EnumSimple approveStatus;

    private String snapshot;

    @DisplayAsArray
    private List<ProjectChange> change;

    // TODO: 2025/11/4 保存changeSummary

    @DisplayAsArray
    private List<String> draft;

    public static String identifier = "entity.vms.ProjectHistoryDetail";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    protected ProjectHistoryDetail() {
    }

    public ProjectHistoryDetail(HistoryType type) {
        this.type = type.toEnumSimple();
    }

    public ProjectHistoryDetail(HistoryType type, String snapshot, List<ProjectChange> change) {
        this(type);
        this.snapshot = snapshot;
        this.change = change;
    }

    public ProjectHistoryDetail(HistoryType type, String snapshot, List<ProjectChange> change,List<String> draft) {
        this(type, snapshot, change);
        this.draft = draft;
    }
}