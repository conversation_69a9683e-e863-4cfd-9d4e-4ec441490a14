package com.caidaocloud.vms.domain.project.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vms.application.dto.PositionRequirementDto;
import com.caidaocloud.vms.domain.base.entity.BaseEntity;
import com.caidaocloud.vms.domain.project.history.DetailedHistoryFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.beans.BeanUtils;

@Data
public class PositionRequirement extends DetailedHistoryFormat<PositionRequirement> {

    private String positionId;

    private DictSimple workExperience;

    private DictSimple education;

    private Integer minSalary;

    private Integer maxSalary;

    private String description;

    private DictSimple skill;

    private Attachment attachment;

    public static String identifier = "entity.vms.PositionRequirement";

    @Override
    public String getEntityIdentifier() {
        return identifier;
    }

    public void update(PositionRequirementDto requirementDto) {
        BeanUtils.copyProperties(requirementDto, this);
        workExperience = new DictSimple();
        education = new DictSimple();
        skill = new DictSimple();
        workExperience.setValue(requirementDto.getWorkExperience());
        education.setValue(requirementDto.getEducation());
        skill.setValue(requirementDto.getSkill());
        super.update();
    }
}