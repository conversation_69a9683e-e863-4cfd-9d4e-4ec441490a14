package com.caidaocloud.vms.domain.project.factory;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vms.domain.project.entity.*;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.history.DataSimpleHistoryFormat;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 项目历史工厂类
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Slf4j
@Component
public class ProjectHistoryFactory {

	@Autowired
	private ProjectDraftRepository projectDraftRepository;



	private final ObjectMapper objectMapper = new ObjectMapper();
	//
	// @Deprecated
	// public static void generateHistoryDetail(Object currentEntity, Object
	// originalEntity, HistoryType historyType,
	// OperationType operationType) {
	// // TODO: 2025/10/10 改为使用draft比对生成detail
	// try {
	// // 获取当前活跃的历史记录ID（从ThreadLocal或其他方式）
	// String historyId = HistoryContext.getHistoryContextInfo().getHistoryId();
	// boolean postWorkflowEnabled =
	// HistoryContext.getHistoryContextInfo().isPostWorkflowEnabled();
	//
	// // 创建历史详情记录
	// ProjectHistoryDetail detail;
	// // 岗位单独审批
	// if (historyType == HistoryType.POSITION && postWorkflowEnabled) {
	// detail = new PositionHistoryDetail(historyId, historyType, operationType);
	// } else {
	// detail = new ProjectHistoryDetail(historyId, historyType);
	// }
	// // 生成快照和变更记录
	// List<ProjectChange> changes = detail.generateChanges(originalEntity,
	// currentEntity, historyType,
	// operationType);
	// // List<ProjectChange> changes = generateChanges(originalEntity,
	// currentEntity,
	// // historyType, operationType);
	//
	// detail.setSnapshot(FastjsonUtil.toJson(originalEntity));
	// detail.setResult(FastjsonUtil.toJson(currentEntity));
	// detail.setChange(changes);
	//
	// // 保存历史详情
	// // HistoryContext.getHistoryContextInfo().cacheDetailRecord(detail);
	//
	// log.debug("生成历史详情记录，类型: {}", historyType);
	//
	// } catch (Exception e) {
	// log.error("生成历史详情记录失败", e);
	// }
	// }

	public static ProjectDraft createDraft(String projectId, String positionId, HistoryType historyType, HistoryType subType, OperationType operationType,
			Optional<DataSimple> originalEntity, DataSimple entity) {
		try {
			// 获取实体的bid作为targetId
			String targetId = null;
			if (entity != null) {
				targetId = entity.getBid();
			}

			if (targetId == null) {
				log.warn("无法获取实体ID，跳过草稿保存");
				return null;
			}

			ProjectDraft draft = new ProjectDraft();
			draft.setTargetId(targetId);
			draft.setProjectId(projectId);
			draft.setPositionId(positionId);
			draft.setType(historyType.toEnumSimple());
			draft.setSubType(subType.toEnumSimple());
			draft.setOperation(operationType.toEnumSimple());

			if (originalEntity.isPresent()) {
				// 序列化实体为JSON快照
				String snapshot = FastjsonUtil.toJson(originalEntity.get());
				draft.setOrigin(snapshot);
			}
			// 序列化实体为JSON快照
			String snapshot = FastjsonUtil.toJson(entity);
			draft.setSnapshot(snapshot);

			return draft;
		} catch (Exception e) {
			log.error("生成草稿失败", e);
			throw e;
		}
	}

	public <T extends DataSimpleHistoryFormat> ProjectHistoryDetail generateHistoryDetailFromDraft(HistoryType historyType, List<T> originalList, List<ProjectDraft> drafts) {
		// 创建历史详情记录
		List<ProjectChange> changeList = new ArrayList<>();
		List<DataSimple> snapshotList = new ArrayList<>();
		List<String> draftIds = new ArrayList<>();
		for (DataSimpleHistoryFormat originData : originalList) {
			Option<ProjectDraft> draftOption = Sequences.sequence(drafts)
					.find(d -> d.getTargetId().equals(originData.getBid()));
			if (draftOption.isDefined()) {
				changeList.addAll(originData.format(draftOption.get()));
				snapshotList.add(FastjsonUtil.toObject(draftOption.get().getSnapshot(), DataSimple.class));
				draftIds.add(draftOption.get().getBid());
			}
			else {
				snapshotList.add(originData);
			}
		}
		ProjectHistoryDetail detail = new ProjectHistoryDetail(historyType);
		detail.setSnapshot(FastjsonUtil.toJson(snapshotList));
		detail.setChange(changeList);
		detail.setDraft(draftIds);
		return detail;
	}

	/**
	 * 根据原始数据和ProjectDraft生成HistoryDetail
	 *
	 * @param originalEntity 原始实体数据
	 * @param projectDraft   项目草稿
	 * @return 历史详情记录
	 */
	public <T> ProjectHistoryDetail generateHistoryDetailFromDraft(DataSimpleHistoryFormat<T> originalEntity, ProjectDraft projectDraft) {
		// 使用实体的format方法生成变更记录

		List<ProjectChange> changes = generateChanges(originalEntity, projectDraft);
		return generateHistoryDetailFromDraft(originalEntity, projectDraft, changes);
	}

	/**
	 * 根据原始数据和ProjectDraft生成HistoryDetail
	 *
	 * @param originalEntity 原始实体数据
	 * @param projectDraft   项目草稿
	 * @return 历史详情记录
	 */
	public <T> ProjectHistoryDetail generateHistoryDetailFromDraft(DataSimpleHistoryFormat<T> originalEntity, ProjectDraft projectDraft, List<ProjectChange> changes) {
		try {
			// 获取草稿中的操作类型和历史类型
			OperationType operationType = OperationType.fromValue(projectDraft.getOperation());
			HistoryType historyType = HistoryType.fromValue(projectDraft.getType().getValue());

			// // 反序列化草稿中的实体数据
			// T draftEntity = originalEntity.deserializeDraftEntity(projectDraft);

			// 创建历史详情记录
			ProjectHistoryDetail detail = new ProjectHistoryDetail(historyType);

			// 设置快照和结果
			detail.setSnapshot(projectDraft.getSnapshot());
			detail.setChange(changes);
			detail.setDraft(Lists.list(projectDraft.getBid()));

			log.debug("根据草稿生成历史详情记录，targetId: {}, 类型: {}, 操作: {}",
					projectDraft.getTargetId(), historyType, operationType);

			return detail;

		}
		catch (Exception e) {
			log.error("根据草稿生成历史详情记录失败，targetId: {}", projectDraft.getTargetId(), e);
			throw new ServerException("生成历史详情记录失败", e);
		}
	}

	/**
	 * 使用实体的format方法生成变更记录
	 */
	private <T> List<ProjectChange> generateChanges(DataSimpleHistoryFormat<T> originData, ProjectDraft projectDraft) {
		try {
				return originData.format(projectDraft);
		} catch (Exception e) {
			throw new ServerException("生成变更记录失败", e);
		}
	}

	public <T> ProjectHistoryDetail generateHistoryDetailFromDraft(DataSimpleHistoryFormat<T> originData, DataSimpleHistoryFormat<T> data, OperationType operation, ProjectDraft draft) {
		List<ProjectChange> list = operation == OperationType.CREATE ? data.format(null, draft) : originData.format(draft);
		return generateHistoryDetailFromDraft(originData, draft, list);
	}

}
