package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.vms.application.dto.PositionManagementQueryDTO;
import com.caidaocloud.vms.application.dto.base.CompanyInfoDto;
import com.caidaocloud.vms.application.dto.base.OrgInfoDto;
import com.caidaocloud.vms.application.dto.base.PostInfoDto;
import com.caidaocloud.vms.application.service.emp.CompanyService;
import com.caidaocloud.vms.application.service.emp.OrganizeService;
import com.caidaocloud.vms.application.service.emp.PostService;
import com.caidaocloud.vms.application.vo.PositionManagementPageVO;
import com.caidaocloud.vms.application.vo.PositionSelectVO;
import com.caidaocloud.vms.domain.project.entity.PositionSupplier;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.enums.QuotationMode;
import com.caidaocloud.vms.domain.project.repository.PositionSupplierRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectPositionRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 岗位管理服务
 * 
 * <AUTHOR> Zhou
 * @date 2025/10/23
 */
@Service
public class PositionManagementService {

    @Autowired
    private ProjectPositionRepository projectPositionRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private PostService postService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private PositionSupplierRepository positionSupplierRepository;

    /**
     * 用工方分页查询所有项目岗位
     * 
     * @param queryDTO 查询条件
     * @return 岗位分页结果
     */
    public PageResult<PositionManagementPageVO> loadPositionPage(PositionManagementQueryDTO queryDTO) {
        // 分页查询岗位数据
        PageResult<ProjectPosition> positionPage = projectPositionRepository.positionManagePage(queryDTO);

        // 组装展示数据
        return assemblePositionPageVO(positionPage);
    }

    /**
     * 组装岗位分页展示数据
     * 
     * @param positionPage 岗位分页数据
     * @return 组装后的展示数据
     */
    private PageResult<PositionManagementPageVO> assemblePositionPageVO(PageResult<ProjectPosition> positionPage) {
        List<ProjectPosition> positions = positionPage.getItems();
        if (positions.isEmpty()) {
            return new PageResult<>(new ArrayList<>(), positionPage.getPageNo(), positionPage.getPageSize(),
                    positionPage.getTotal());
        }

        // 收集需要查询的ID列表
        List<String> projectIds = positions.stream().map(ProjectPosition::getProjectId).distinct()
                .collect(Collectors.toList());
        List<String> positionIds = positions.stream().map(ProjectPosition::getPosition).distinct()
                .collect(Collectors.toList());
        List<String> companyIds = positions.stream().map(ProjectPosition::getCompany).distinct()
                .collect(Collectors.toList());
        // 批量查询关联数据
        List<Project> projects = projectRepository.loadList(projectIds);
        List<PostInfoDto> postList = postService.loadPostList(positionIds);
        List<CompanyInfoDto> companyList = companyService.loadCompanyList(companyIds);

        // 组装VO
        List<PositionManagementPageVO> voList = positions.stream().map(position -> {
            PositionManagementPageVO vo = ObjectConverter.convert(position, PositionManagementPageVO.class);

            // 组装项目信息
            Option<Project> projectOpt = Sequences.sequence(projects)
                    .find(project -> position.getProjectId().equals(project.getBid()));
            if (projectOpt.isDefined()) {
                Project project = projectOpt.get();
                vo.setProjectName(project.getProjectName());
                vo.setProjectCode(project.getProjectCode());
            }

            // 组装岗位信息
            Option<PostInfoDto> postOpt = Sequences.sequence(postList)
                    .find(post -> position.getPosition().equals(post.getBid()));
            if (postOpt.isDefined()) {
                PostInfoDto post = postOpt.get();
                vo.setPositionName(post.getName());
                vo.setPositionCode(post.getCode());
            }

            // 组装公司信息
            Option<CompanyInfoDto> companyOpt = Sequences.sequence(companyList)
                    .find(company -> position.getCompany().equals(company.getBid()));
            if (companyOpt.isDefined()) {
                vo.setCompanyName(companyOpt.get().getCompanyName());
            }

            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(voList, positionPage.getPageNo(), positionPage.getPageSize(), positionPage.getTotal());
    }

    /**
     * 供应商查询关联岗位下拉列表
     *
     * @param queryDTO 查询条件
     * @return 岗位下拉列表
     */
    public List<PositionSelectVO> loadSupplierPositionSelectList(PositionManagementQueryDTO queryDTO) {
        List<PositionSupplier> positionSupplierList = positionSupplierRepository.listByProjectIdSupplierId(queryDTO.getProjectId(), queryDTO.getSupplierId());
        if (positionSupplierList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> positionIds = Sequences.sequence(positionSupplierList).map(PositionSupplier::getPositionId).toList();

        List<PostInfoDto> postList = postService.loadPostList(positionIds);

        // 组装VO
        List<PositionSelectVO> voList = positionSupplierList.stream().map(position -> {
            PositionSelectVO vo = ObjectConverter.convert(position, PositionSelectVO.class);
            vo.setQuotationMode(QuotationMode.fromValue(position.getQuotationMode()));

            // 组装岗位信息
            Option<PostInfoDto> postOpt = Sequences.sequence(postList)
                    .find(post -> position.getPositionId().equals(post.getBid()));
            if (postOpt.isDefined()) {
                PostInfoDto post = postOpt.get();
                vo.setPositionName(post.getName());
                vo.setPositionCode(post.getCode());
            }
            return vo;
        }).collect(Collectors.toList());

        return voList;
    }
}
