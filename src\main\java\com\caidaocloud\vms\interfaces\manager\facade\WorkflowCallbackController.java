package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.vms.application.service.WorkflowCallbackService;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流回调Controller
 * 处理工作流审批结果的回调
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
@RestController
@RequestMapping("/api/vms/v1/manager")
@Api(tags = "工作流回调管理")
@Slf4j
public class WorkflowCallbackController {

    @Autowired
    private WorkflowCallbackService workflowCallbackService;

    /**
     * 项目工作流审批通过回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/workflow/approve")
    @ApiOperation(value = "项目工作流审批通过")
    public Result<Void> approveProjectWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("项目工作流审批通过回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
        return Result.success();
    }

    /**
     * 项目工作流审批拒绝回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/workflow/reject")
    @ApiOperation(value = "项目工作流审批拒绝", notes = "工作流审批拒绝后的回调处理")
    public Result<Void> rejectProjectWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目工作流审批拒绝回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
        return Result.success();
    }

    /**
     * 项目岗位工作流审批通过回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/position/workflow/approve")
    @ApiOperation(value = "项目岗位工作流审批通过", notes = "项目岗位工作流审批通过后的回调处理")
    public Result<Void> approveProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目岗位工作流审批通过回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
        return Result.success();
    }

    /**
     * 项目岗位工作流审批拒绝回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/position/workflow/reject")
    @ApiOperation(value = "项目岗位工作流审批拒绝", notes = "项目岗位工作流审批拒绝后的回调处理")
    public Result<Void> rejectProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目岗位工作流审批拒绝回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
        return Result.success();
    }
}
