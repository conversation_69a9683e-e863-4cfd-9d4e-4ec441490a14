package com.caidaocloud.vms.application.dto;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "供应商基本信息")
public class SupplierDto {
    @ApiModelProperty(value = "供应商ID", example = "123456")
    private String bid;

    @ApiModelProperty(value = "供应商编码", example = "SUP-001", required = true)
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", example = "ABC科技有限公司", required = true)
    private String supplierName;

    @ApiModelProperty(value = "国家/地区")
    private DictSimple country;

    @ApiModelProperty(value = "省份/州")
    private Address province;

    @ApiModelProperty(value = "邮政编码", example = "100000")
    private String postalCode;

    @ApiModelProperty(value = "联系电话", example = "010-12345678")
    private String phone;

    @ApiModelProperty(value = "详细地址", example = "北京市海淀区中关村大街1号")
    private String address;

    @ApiModelProperty(value = "备注信息")
    private String remarks;

    @ApiModelProperty(value = "员工规模")
    private DictSimple staffSize;

    @ApiModelProperty(value = "营收规模")
    private DictSimple revenueScale;

    @ApiModelProperty(value = "所属行业", example = "[\"IT\", \"电子\"]")
    private List<String> industry;

    @ApiModelProperty(value = "评级", example = "A")
    private String rating;
}
