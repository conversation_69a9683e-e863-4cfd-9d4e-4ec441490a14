package com.caidaocloud.vms.domain.project.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.ProjectHistoryQueryDTO;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;

import java.util.List;
import java.util.Optional;

/**
 * 项目历史Repository接口
 * 
 * <AUTHOR>
 * @date 2025/9/25
 */
public interface ProjectHistoryRepository {

    /**
     * 保存或更新项目历史记录
     * 
     * @param projectHistory 项目历史记录
     * @return 历史记录ID
     */
    String saveOrUpdate(ProjectHistory projectHistory);

    /**
     * 根据ID获取项目历史记录
     * 
     * @param historyId 历史记录ID
     * @return 项目历史记录
     */
    Optional<ProjectHistory> getById(String historyId);

    /**
     * 根据项目ID获取历史记录列表
     * 
     * @param projectId 项目ID
     * @return 历史记录列表
     */
    List<ProjectHistory> getByProjectId(String projectId);

    /**
     * 分页查询项目历史记录
     * 
     * @param projectId 项目ID
     * @param pageSize  页大小
     * @param pageNo    页码
     * @return 分页结果
     */
    PageResult<ProjectHistory> findByPage(String projectId, int pageSize, int pageNo);

    /**
     * 删除项目历史记录
     * 
     * @param historyId 历史记录ID
     */
    void delete(String historyId);

    Optional<ProjectHistory> loadTempByProjectIdAndUser(String projectId);

    void rollbackHistory(ProjectHistory projectHistory);

}
